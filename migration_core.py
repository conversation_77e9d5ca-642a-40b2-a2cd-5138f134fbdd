"""
数据库迁移工具 - 核心逻辑模块
---------------------------------
本文件包含所有与数据库交互、执行迁移任务的实际功能。
它不包含任何用户界面代码，可以被任何界面 (GUI, CLI) 调用。
"""
import mysql.connector
import re
from mysql.connector import errorcode

# --- 配置常量 ---

# 每次从源数据库读取的数据行数
BATCH_SIZE = 1000

# 定义要忽略的系统数据库
SYSTEM_DATABASES = {'information_schema', 'mysql', 'performance_schema', 'sys'}

# 定义要忽略的系统用户/角色
SYSTEM_USER_NAMES = {
    'root',
    'mysql',
    'mysql.sys',
    'mysql.session',
    'mysql.infoschema',
    'mariadb.sys',
    'debian-sys-maint',
    'PUBLIC'
}


# --- 核心迁移函数 ---

def log_message(logger, message):
    """通过回调函数记录日志，以便UI可以显示"""
    if logger:
        logger(message)

def create_safe_connection(config, logger=None):
    """创建安全的数据库连接，确保字符集正确"""
    try:
        # 使用最基本的连接参数，避免字符集冲突
        connection_params = {
            'host': config['host'],
            'port': config['port'],
            'user': config['user'],
            'password': config['password'],
            'use_unicode': True,
            'autocommit': True
        }

        # 获取字符集参数，如果没有则使用utf8mb4作为默认值
        charset = config.get('charset', 'utf8mb4')
        
        # 特别处理：将'utf8'转换为'utf8mb3'，因为某些MySQL版本不支持直接使用'utf8'
        if charset == 'utf8':
            charset = 'utf8mb3'
            if logger:
                log_message(logger, "调试: 字符集'utf8'已自动转换为'utf8mb3'")
        
        # 如果有连接超时参数，添加它
        if 'connection_timeout' in config:
            connection_params['connection_timeout'] = config['connection_timeout']

        if logger:
            log_message(logger, f"调试: 连接参数 - host={config['host']}, port={config['port']}, user={config['user']}, charset={charset}")

        # 使用与测试连接相同的方式 - 这是唯一成功的方法
        cnx = mysql.connector.connect(
            host=connection_params['host'],
            port=connection_params['port'],
            user=connection_params['user'],
            password=connection_params['password'],
            use_unicode=True,
            autocommit=connection_params.get('autocommit', True),
            connection_timeout=connection_params.get('connection_timeout', 30)
        )

        cursor = cnx.cursor()

        # 连接成功后，设置字符集（使用配置中指定的字符集）
        try:
            cursor.execute(f"SET NAMES {charset}")
            cursor.execute(f"SET CHARACTER SET {charset}")
        except mysql.connector.Error:
            # 如果设置失败，不抛出错误，使用默认字符集
            if logger:
                log_message(logger, "调试: 字符集设置失败，使用默认字符集")

        # 验证字符集设置
        cursor.execute("SELECT @@character_set_connection, @@collation_connection")
        charset_info = cursor.fetchone()

        if logger:
            log_message(logger, f"✓ 数据库连接成功 - 字符集: {charset_info[0]}, 排序规则: {charset_info[1]}")

        cursor.close()
        return cnx

    except mysql.connector.Error as err:
        if logger:
            log_message(logger, f"✗ 数据库连接失败: {err}")
            log_message(logger, f"调试: 错误详情 - errno: {err.errno}, sqlstate: {getattr(err, 'sqlstate', 'N/A')}")
        raise

def test_db_connection(config, logger):
    """测试数据库连接"""
    try:
        log_message(logger, f"正在测试连接到 {config.get('host')}...")
        # 创建测试连接配置，确保字符集兼容性
        test_config = config.copy()
        # 移除可能冲突的参数
        test_config.pop('collation', None)
        test_config.pop('ssl_disabled', None)

        # 不覆盖原有的字符集设置
        if 'charset' not in test_config:
            test_config['charset'] = 'utf8mb4'
        test_config['use_unicode'] = True
        
        # 添加连接超时参数
        test_config['connection_timeout'] = 5
        cnx = create_safe_connection(test_config, logger)
        cnx.close()
        log_message(logger, "连接成功！")
        return True
    except mysql.connector.Error as err:
        log_message(logger, f"连接失败: {err}")
        return False
    except Exception as e:
        log_message(logger, f"发生未知错误: {e}")
        return False

def migrate_data_and_structure(source_config, dest_config, logger):
    """
    迁移所有非系统数据库的结构和数据。
    """
    source_cnx, source_cursor, dest_cnx, dest_cursor = None, None, None, None
    try:
        log_message(logger, "--- 开始迁移数据和结构 ---")
        # 优化字符集配置，解决兼容性问题
        source_config = source_config.copy()  # 避免修改原始配置
        dest_config = dest_config.copy()

        # 清理配置，但保留字符集设置
        for config in [source_config, dest_config]:
            # 只移除可能导致冲突的参数，保留字符集参数
            config.pop('collation', None)
            config.pop('ssl_disabled', None)
            config.pop('sql_mode', None)
            # 确保use_unicode为True
            config['use_unicode'] = True
        
        # 连接数据库
        log_message(logger, "连接源数据库...")
        try:
            source_cnx = create_safe_connection(source_config, logger)
            source_cursor = source_cnx.cursor()
        except mysql.connector.Error as err:
            raise

        log_message(logger, "连接目标数据库...")
        try:
            dest_cnx = create_safe_connection(dest_config, logger)
            dest_cursor = dest_cnx.cursor()
        except mysql.connector.Error as err:
            raise
        
        # 获取并过滤数据库列表
        source_cursor.execute("SHOW DATABASES")
        all_dbs = {db[0] for db in source_cursor.fetchall()}
        user_dbs = sorted(list(all_dbs - SYSTEM_DATABASES))
        log_message(logger, f"发现 {len(user_dbs)} 个用户数据库待迁移: {', '.join(user_dbs)}")

        for db_name in user_dbs:
            log_message(logger, f"\n>> 正在处理数据库: `{db_name}`")
            # 确保数据库使用正确的字符集和排序规则
            create_db_sql = f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
            dest_cursor.execute(create_db_sql)
            dest_cnx.commit()

            source_cursor.execute(f"USE `{db_name}`")
            dest_cursor.execute(f"USE `{db_name}`")

            # 迁移表和数据
            _migrate_all_tables_for_db(source_cursor, dest_cursor, source_cnx, dest_cnx, logger)
            # 迁移事件
            _migrate_all_events_for_db(source_cursor, dest_cursor, logger)
        
        log_message(logger, "\n--- 所有数据和结构迁移成功！ ---")
        return True

    except mysql.connector.Error as err:
        log_message(logger, f"\n[错误] 数据库操作失败: {err}")
        return False
    except Exception as e:
        log_message(logger, f"\n[错误] 发生未知错误: {e}")
        return False
    finally:
        if source_cursor: source_cursor.close()
        if source_cnx and source_cnx.is_connected(): source_cnx.close()
        if dest_cursor: dest_cursor.close()
        if dest_cnx and dest_cnx.is_connected(): dest_cnx.close()
        log_message(logger, "所有数据库连接已关闭。")


def migrate_users_and_permissions(source_config, dest_config, logger, direct_execute=False):
    """
    生成或直接执行用户和权限的迁移。
    """
    source_cnx, source_cursor, dest_cnx, dest_cursor = None, None, None, None
    try:
        log_message(logger, "--- 开始迁移用户和权限 ---")
        # 优化字符集配置，解决兼容性问题
        source_config = source_config.copy()  # 避免修改原始配置
        dest_config = dest_config.copy()

        # 输出字符集配置，用于调试
        log_message(logger, f"调试: 用户迁移阶段 - 源数据库字符集: {source_config.get('charset', '未指定')}")
        log_message(logger, f"调试: 用户迁移阶段 - 目标数据库字符集: {dest_config.get('charset', '未指定')}")

        # 清理配置，但保留字符集设置
        for config in [source_config, dest_config]:
            # 只移除可能导致冲突的参数，保留字符集参数
            config.pop('collation', None)
            config.pop('ssl_disabled', None)
            config.pop('sql_mode', None)
            # 确保use_unicode为True
            config['use_unicode'] = True
        
        log_message(logger, "连接源数据库...")
        source_cnx = create_safe_connection(source_config, logger)
        source_cursor = source_cnx.cursor()

        # 获取用户列表
        source_cursor.execute("SELECT user, host FROM mysql.user")
        users = source_cursor.fetchall()
        non_system_users = [(u, h) for u, h in users if u not in SYSTEM_USER_NAMES]
        log_message(logger, f"发现 {len(non_system_users)} 个非系统用户待迁移。")
        
        sql_commands = []
        for user, host in non_system_users:
            log_message(logger, f"  -> 正在处理用户: '{user}'@'{host}'")
            # 生成 CREATE USER
            sql_commands.append(f"CREATE USER IF NOT EXISTS '{user}'@'{host}' IDENTIFIED WITH mysql_native_password BY 'please_change_password_after_migration';")
            # 生成 GRANTS
            source_cursor.execute(f"SHOW GRANTS FOR '{user}'@'{host}'")
            grants = source_cursor.fetchall()
            for grant in grants:
                grant_sql = grant[0]
                if 'IDENTIFIED BY PASSWORD' in grant_sql: continue
                grant_sql = grant_sql.replace('WITH GRANT OPTION', '')
                sql_commands.append(grant_sql + ";")
        
        if direct_execute:
            # 直接执行模式
            log_message(logger, "\n模式: 直接在目标数据库执行命令...")
            log_message(logger, "连接目标数据库...")
            dest_cnx = create_safe_connection(dest_config, logger)
            dest_cursor = dest_cnx.cursor()
            
            for cmd in sql_commands:
                try:
                    dest_cursor.execute(cmd)
                except mysql.connector.Error as err:
                    log_message(logger, f"  [警告] 执行命令失败 (可忽略的语法差异): {cmd[:50]}... - {err}")
            dest_cursor.execute("FLUSH PRIVILEGES;")
            log_message(logger, "所有用户和权限已直接在目标数据库上创建/更新。")
        else:
            # 手动安全模式
            log_message(logger, "\n模式: 生成 user_migration.sql 文件...")
            header = "-- # 用户和权限迁移脚本 (请在目标服务器上执行)\n\n"
            full_sql = header + "\n".join(sql_commands) + "\n\nFLUSH PRIVILEGES;"
            with open('user_migration.sql', 'w', encoding='utf-8') as f:
                f.write(full_sql)
            log_message(logger, "成功！用户迁移脚本已生成: user_migration.sql")
        
        log_message(logger, "\n--- 用户和权限迁移任务完成！ ---")
        return True

    except mysql.connector.Error as err:
        log_message(logger, f"\n[错误] 数据库操作失败: {err}")
        return False
    except Exception as e:
        log_message(logger, f"\n[错误] 发生未知错误: {e}")
        return False
    finally:
        if source_cursor: source_cursor.close()
        if source_cnx and source_cnx.is_connected(): source_cnx.close()
        if dest_cursor: dest_cursor.close()
        if dest_cnx and dest_cnx.is_connected(): dest_cnx.close()


# --- 内部辅助函数 ---

def _migrate_all_tables_for_db(source_cursor, dest_cursor, source_cnx, dest_cnx, logger):
    """迁移单个数据库内的所有表"""
    source_cursor.execute("SHOW TABLES")
    tables = [table[0] for table in source_cursor.fetchall()]
    if not tables:
        log_message(logger, "   - 该数据库为空，跳过。")
        return
        
    log_message(logger, f"   - 发现 {len(tables)} 个表...")
    dest_cursor.execute("SET FOREIGN_KEY_CHECKS=0;")

    for table_name in tables:
        # 迁移结构
        source_cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
        create_table_sql = source_cursor.fetchone()[1]
        create_table_sql = create_table_sql.replace('DEFAULT unix_timestamp()', '')
        dest_cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
        dest_cursor.execute(create_table_sql)

        # 迁移数据
        source_cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
        row_count = source_cursor.fetchone()[0]
        if row_count == 0:
            log_message(logger, f"     - 表 `{table_name}` 为空，跳过数据迁移")
            continue

        log_message(logger, f"     - 正在迁移表 `{table_name}` ({row_count} 行)")
        data_cursor = source_cnx.cursor(dictionary=True)
        data_cursor.execute(f"SELECT * FROM `{table_name}`")

        migrated_rows = 0
        while True:
            rows = data_cursor.fetchmany(BATCH_SIZE)
            if not rows: break

            cols = list(rows[0].keys())
            cols_sql = ", ".join([f"`{c}`" for c in cols])
            placeholders = ", ".join(["%s"] * len(cols))
            insert_sql = f"INSERT INTO `{table_name}` ({cols_sql}) VALUES ({placeholders})"

            values = [tuple(row[c] for c in cols) for row in rows]
            dest_cursor.executemany(insert_sql, values)
            dest_cnx.commit()

            migrated_rows += len(rows)
            if migrated_rows % (BATCH_SIZE * 10) == 0:  # 每10个批次报告一次进度
                log_message(logger, f"       已迁移 {migrated_rows}/{row_count} 行...")

        log_message(logger, f"     - 表 `{table_name}` 迁移完成 ({migrated_rows} 行)")
        data_cursor.close()
    
    dest_cursor.execute("SET FOREIGN_KEY_CHECKS=1;")
    log_message(logger, "   - 所有表迁移完成。")


def _migrate_all_events_for_db(source_cursor, dest_cursor, logger):
    """迁移单个数据库内的所有事件"""
    try:
        source_cursor.execute("SHOW EVENTS")
        events = source_cursor.fetchall()
        if not events: return
            
        log_message(logger, f"   - 发现 {len(events)} 个事件...")
        for event_data in events:
            event_name = event_data[1]
            source_cursor.execute(f"SHOW CREATE EVENT `{event_name}`")
            create_event_sql = source_cursor.fetchone()[3]
            create_event_sql_cleaned = re.sub(r'DEFINER=`[^`]+`@`[^`]+`\s', '', create_event_sql, flags=re.IGNORECASE).strip()
            dest_cursor.execute(f"DROP EVENT IF EXISTS `{event_name}`")
            dest_cursor.execute(create_event_sql_cleaned)
        log_message(logger, "   - 所有事件迁移完成。")
    except mysql.connector.Error as err:
        if err.errno == 1370: # 权限不足
             log_message(logger, "   - [警告] 当前用户无权限查询事件，已跳过。")
        else:
             log_message(logger, f"   - [错误] 迁移事件失败: {err}") 