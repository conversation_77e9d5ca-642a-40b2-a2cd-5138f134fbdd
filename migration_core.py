"""
数据库迁移工具 - 核心逻辑模块
---------------------------------
本文件包含所有与数据库交互、执行迁移任务的实际功能。
它不包含任何用户界面代码，可以被任何界面 (GUI, CLI) 调用。
"""
import pymysql
import re

# --- 配置常量 ---

# 每次从源数据库读取的数据行数
BATCH_SIZE = 1000

# 定义要忽略的系统数据库
SYSTEM_DATABASES = {'information_schema', 'mysql', 'performance_schema', 'sys'}

# 定义要忽略的系统用户/角色
SYSTEM_USER_NAMES = {
    'root',
    'mysql',
    'mysql.sys',
    'mysql.session',
    'mysql.infoschema',
    'mariadb.sys',
    'debian-sys-maint',
    'PUBLIC'
}


# --- 核心迁移函数 ---

def log_message(logger, message):
    """通过回调函数记录日志，以便UI可以显示"""
    if logger:
        logger(message)

def create_safe_connection(config, logger=None):
    """使用PyMySQL创建数据库连接"""
    try:
        if logger:
            # 记录连接参数时隐藏密码
            log_config = config.copy()
            log_config['password'] = '******'
            log_message(logger, f"调试: 正在使用PyMySQL连接, 参数: {log_config}")

        # PyMySQL直接使用charset，不需要use_unicode
        # autocommit在这里设置为True，简化后续操作
        cnx = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            charset=config.get('charset', 'utf8mb4'),
            autocommit=True,
            connect_timeout=30,
            cursorclass=pymysql.cursors.DictCursor  # 使用字典游标，方便按列名获取数据
        )

        with cnx.cursor() as cursor:
             # 验证连接和字符集
            cursor.execute("SELECT @@character_set_connection, @@collation_connection")
            charset_info = cursor.fetchone()
            if logger:
                log_message(logger, f"✓ 数据库连接成功 - 字符集: {charset_info['@@character_set_connection']}, 排序规则: {charset_info['@@collation_connection']}")
        
        return cnx

    except pymysql.MySQLError as err:
        if logger:
            log_message(logger, f"✗ 数据库连接失败: {err}")
        raise

def test_db_connection(config, logger):
    """使用PyMySQL测试数据库连接"""
    cnx = None
    try:
        log_message(logger, f"正在测试连接到 {config.get('host')}...")

        connection_params = {
            'host': config['host'],
            'port': config['port'],
            'user': config['user'],
            'password': config['password'],
            'charset': config.get('charset', 'utf8mb4'),
            'connect_timeout': 5
        }
        
        if logger:
            log_config = connection_params.copy()
            log_config['password'] = '******'
            log_message(logger, f"调试: 测试连接参数 - {log_config}")

        cnx = pymysql.connect(**connection_params)
        
        with cnx.cursor() as cursor:
            # 简单验证连接
            cursor.execute("SELECT 1")
            cursor.fetchone()
            # 获取字符集信息
            cursor.execute("SELECT @@character_set_connection, @@collation_connection")
            charset_info = cursor.fetchone()

        if logger:
            log_message(logger, f"✓ 数据库连接成功 - 字符集: {charset_info[0]}, 排序规则: {charset_info[1]}")

        return True

    except pymysql.MySQLError as err:
        log_message(logger, f"连接失败: {err}")
        return False
    except Exception as e:
        log_message(logger, f"发生未知错误: {e}")
        return False
    finally:
        if cnx and cnx.open:
            cnx.close()
            if logger:
                log_message(logger, "调试: 测试连接已关闭。")

def migrate_data_and_structure(source_config, dest_config, logger):
    """
    迁移所有非系统数据库的结构和数据。
    """
    source_cnx, dest_cnx = None, None
    try:
        log_message(logger, "--- 开始迁移数据和结构 ---")

        # 连接数据库
        log_message(logger, "连接源数据库...")
        source_cnx = create_safe_connection(source_config, logger)
        
        log_message(logger, "连接目标数据库...")
        dest_cnx = create_safe_connection(dest_config, logger)
        
        with source_cnx.cursor() as source_cursor:
            # 获取并过滤数据库列表
            source_cursor.execute("SHOW DATABASES")
            all_dbs = {row['Database'] for row in source_cursor.fetchall()}
            user_dbs = sorted(list(all_dbs - SYSTEM_DATABASES))
            log_message(logger, f"发现 {len(user_dbs)} 个用户数据库待迁移: {', '.join(user_dbs)}")

            for db_name in user_dbs:
                with dest_cnx.cursor() as dest_cursor:
                    log_message(logger, f"\n>> 正在处理数据库: `{db_name}`")
                    # 确保数据库使用正确的字符集和排序规则
                    create_db_sql = f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
                    dest_cursor.execute(create_db_sql)
                    dest_cnx.commit()

                    log_message(logger, f"  -> 切换到数据库 `{db_name}`")
                    source_cursor.execute(f"USE `{db_name}`")
                    dest_cursor.execute(f"USE `{db_name}`")

                    # 迁移表结构和数据
                    _migrate_all_tables_for_db(source_cursor, dest_cursor, source_cnx, dest_cnx, logger, db_name)
                    
                    # 迁移存储过程和函数
                    _migrate_routines_for_db(source_cursor, dest_cursor, logger, 'PROCEDURE', db_name)
                    _migrate_routines_for_db(source_cursor, dest_cursor, logger, 'FUNCTION', db_name)

                    # 迁移视图
                    _migrate_views_for_db(source_cursor, dest_cursor, logger, db_name)
                    
                    # 迁移触发器
                    _migrate_triggers_for_db(source_cursor, dest_cursor, logger)
                    
                    # 迁移事件
                    _migrate_all_events_for_db(source_cursor, dest_cursor, logger)

        log_message(logger, "\n--- 数据和结构迁移任务完成！ ---")
        return True

    except pymysql.MySQLError as err:
        log_message(logger, f"\n[数据库错误] 迁移中断: {err}")
        return False
    except Exception as e:
        log_message(logger, f"\n[致命错误] 发生未知异常: {e}")
        import traceback
        log_message(logger, traceback.format_exc())
        return False
    finally:
        _safe_close_connections([source_cnx, dest_cnx], logger)
        log_message(logger, "所有数据库连接已关闭。")

def migrate_users_and_permissions(source_config, dest_config, logger, direct_execute=False):
    """
    迁移所有非系统用户的账户和权限。
    """
    source_cnx, dest_cnx = None, None
    all_grant_commands = []

    try:
        log_message(logger, "--- 开始迁移用户和权限 ---")

        source_cnx = create_safe_connection(source_config, logger)
        with source_cnx.cursor() as source_cursor:
            log_message(logger, "连接源数据库...")
            
            # 使用 mysql.user 表获取所有用户
            source_cursor.execute("SELECT user, host FROM mysql.user")
            all_users = source_cursor.fetchall()
            
            # 过滤掉系统用户
            user_accounts = [
                (user['user'], user['host']) for user in all_users 
                if user['user'] not in SYSTEM_USER_NAMES and not user['user'].startswith('mysql.')
            ]
            
            log_message(logger, f"发现 {len(user_accounts)} 个非系统用户待迁移。")

            for user, host in user_accounts:
                log_message(logger, f"  -> 正在处理用户: '{user}'@'{host}'")
                source_cursor.execute(f"SHOW GRANTS FOR '{user}'@'{host}'")
                grants = source_cursor.fetchall()
                
                for grant in grants:
                    # PyMySQL DictCursor返回字典，键是列名
                    grant_command = list(grant.values())[0]
                    # 修正MariaDB中 'REQUIRE NONE' 为 'REQUIRE NONE'
                    grant_command = grant_command.replace("REQUIRE NONE", "REQUIRE NONE WITH GRANT OPTION")
                    all_grant_commands.append(grant_command + ";")
            
        if not all_grant_commands:
            log_message(logger, "没有发现任何用户需要迁移。")
            return True

        if direct_execute:
            log_message(logger, "\n模式: 直接在目标数据库执行命令...")
            dest_cnx = create_safe_connection(dest_config, logger)
            with dest_cnx.cursor() as dest_cursor:
                log_message(logger, "连接目标数据库...")
                
                for command in all_grant_commands:
                    try:
                        dest_cursor.execute(command)
                    except pymysql.MySQLError as e:
                        # 忽略用户已存在的错误，这很常见
                        if e.args[0] == 1396: # Operation CREATE USER failed
                            log_message(logger, f"警告: 用户可能已存在，跳过创建: {command.split(';')[0]}")
                        else:
                            log_message(logger, f"警告: 执行失败 (可忽略): `{command}` -> {e}")
                
                dest_cnx.commit()
            log_message(logger, "所有用户和权限已直接在目标数据库上创建/更新。")
        else:
            log_message(logger, "\n模式: 生成SQL文件...")
            filename = "user_grants.sql"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("/*-- 由数据库迁移工具生成的用户和权限脚本 --*/\n\n")
                f.write("FLUSH PRIVILEGES;\n\n")
                for command in all_grant_commands:
                    f.write(f"{command}\n")
            log_message(logger, f"所有用户和权限的SQL命令已保存到 `{filename}`。请在目标数据库手动执行此文件。")

        log_message(logger, "\n--- 用户和权限迁移任务完成！ ---")
        return True

    except pymysql.MySQLError as err:
        log_message(logger, f"[数据库错误] 用户迁移中断: {err}")
        return False
    except Exception as e:
        log_message(logger, f"[致命错误] 用户迁移时发生未知异常: {e}")
        return False
    finally:
        _safe_close_connections([source_cnx, dest_cnx], logger)

def _safe_close_connections(connections, logger=None):
    """安全地关闭一个或多个数据库连接"""
    if not isinstance(connections, list):
        connections = [connections]
    
    for cnx in connections:
        if cnx and cnx.open:
            try:
                cnx.close()
                if logger:
                    log_message(logger, f"调试: 数据库连接已关闭")
            except Exception as e:
                if logger:
                    log_message(logger, f"调试: 关闭连接时出错: {e}")

def _migrate_all_tables_for_db(source_cursor, dest_cursor, source_cnx, dest_cnx, logger, db_name):
    """迁移数据库中的所有表"""
    source_cursor.execute("SHOW FULL TABLES WHERE Table_type = 'BASE TABLE'")
    # 修正：直接使用传入的db_name，而不是依赖不稳定的connection.db
    tables = [row['Tables_in_' + db_name] for row in source_cursor.fetchall()]
    
    if not tables:
        log_message(logger, "  -> 此数据库中没有表。")
        return

    log_message(logger, f"  -> 发现 {len(tables)} 个表: {', '.join(tables)}")

    # 禁用外键检查，加速导入
    dest_cursor.execute("SET FOREIGN_KEY_CHECKS=0;")

    for table_name in tables:
        log_message(logger, f"\n    -- 正在处理表: `{table_name}` --")
        
        # 1. 迁移表结构
        log_message(logger, f"      - 正在迁移表结构...")
        source_cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
        create_table_sql = source_cursor.fetchone()['Create Table']
        
        # 删除 AUTO_INCREMENT，避免导入冲突
        create_table_sql = re.sub(r'AUTO_INCREMENT=\d+\s', '', create_table_sql)
        # 兼容性修复：移除部分版本不支持的 DEFAULT unix_timestamp()
        create_table_sql = re.sub(r'\sDEFAULT\s+unix_timestamp\(\)', '', create_table_sql, flags=re.IGNORECASE)

        try:
            # 先删除目标表，确保结构最新
            dest_cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
            dest_cursor.execute(create_table_sql)
            dest_cnx.commit()
        except pymysql.MySQLError as e:
            log_message(logger, f"[错误] 创建表`{table_name}`失败: {e}")
            continue

        # 2. 迁移数据
        source_cursor.execute(f"SELECT COUNT(*) as count FROM `{table_name}`")
        total_rows = source_cursor.fetchone()['count']
        
        if total_rows == 0:
            log_message(logger, "      - 表中无数据，跳过迁移。")
            continue
            
        log_message(logger, f"      - 正在迁移 {total_rows} 行数据...")

        # 使用无缓冲游标处理大量数据
        with source_cnx.cursor(pymysql.cursors.SSCursor) as sscursor:
            sscursor.execute(f"SELECT * FROM `{table_name}`")
            
            row_count = 0
            while True:
                rows = sscursor.fetchmany(BATCH_SIZE)
                if not rows:
                    break
                
                # 获取列名
                columns = [desc[0] for desc in sscursor.description]
                
                # 构建插入语句
                placeholders = ', '.join(['%s'] * len(columns))
                sql = f"INSERT INTO `{table_name}` (`" + '`, `'.join(columns) + "`) VALUES (" + placeholders + ")"
                
                try:
                    dest_cursor.executemany(sql, rows)
                    dest_cnx.commit()
                    row_count += len(rows)
                    log_message(logger, f"        ...已迁移 {row_count} / {total_rows} 行")
                except pymysql.MySQLError as e:
                    log_message(logger, f"[错误] 插入数据到`{table_name}`失败: {e}")
                    # 跳过这个批次，继续下一个
                    continue
    
    # 重新启用外键检查
    dest_cursor.execute("SET FOREIGN_KEY_CHECKS=1;")
    log_message(logger, "\n  -> 所有表处理完成。")


def _migrate_routines_for_db(source_cursor, dest_cursor, logger, routine_type, db_name):
    """迁移存储过程或函数"""
    log_message(logger, f"\n  -> 正在迁移 {routine_type}...")
    
    query = f"SHOW {routine_type} STATUS WHERE Db = %s"
    # 修正：直接使用传入的db_name
    source_cursor.execute(query, (db_name,))
    routines = source_cursor.fetchall()

    if not routines:
        log_message(logger, f"    - 未发现 {routine_type}。")
        return

    for routine in routines:
        name = routine['Name']
        log_message(logger, f"    - 正在处理 {routine_type}: `{name}`")
        
        create_query = f"SHOW CREATE {routine_type} `{name}`"
        source_cursor.execute(create_query)
        create_statement_row = source_cursor.fetchone()
        
        # 键名可能是 'Create Procedure' 或 'Create Function'
        create_statement = create_statement_row[f'Create {routine_type.capitalize()}']
        
        # 移除 DEFINER，避免权限问题
        create_statement = re.sub(r'DEFINER=`[^`]+`@`[^`]+`\s', '', create_statement, flags=re.IGNORECASE)
        
        try:
            dest_cursor.execute(f"DROP {routine_type} IF EXISTS `{name}`")
            dest_cursor.execute(create_statement)
        except pymysql.MySQLError as e:
            log_message(logger, f"      [警告] 迁移 {routine_type} `{name}` 失败: {e}")

def _migrate_views_for_db(source_cursor, dest_cursor, logger, db_name):
    """迁移数据库中的所有视图"""
    log_message(logger, "\n  -> 正在迁移视图...")
    source_cursor.execute("SHOW FULL TABLES WHERE Table_type = 'VIEW'")
    # 修正：直接使用传入的db_name
    views = [row['Tables_in_' + db_name] for row in source_cursor.fetchall()]

    if not views:
        log_message(logger, "    - 未发现视图。")
        return

    for view_name in views:
        log_message(logger, f"    - 正在处理视图: `{view_name}`")
        try:
            source_cursor.execute(f"SHOW CREATE VIEW `{view_name}`")
            create_view_sql = source_cursor.fetchone()['Create View']
            
            # 移除 DEFINER
            create_view_sql = re.sub(r'DEFINER=`[^`]+`@`[^`]+`\s', '', create_view_sql, flags=re.IGNORECASE)
            
            dest_cursor.execute(f"DROP VIEW IF EXISTS `{view_name}`")
            dest_cursor.execute(create_view_sql)
        except pymysql.MySQLError as e:
            log_message(logger, f"      [警告] 迁移视图 `{view_name}` 失败: {e}")

def _migrate_triggers_for_db(source_cursor, dest_cursor, logger):
    """迁移数据库中的所有触发器"""
    log_message(logger, "\n  -> 正在迁移触发器...")
    source_cursor.execute("SHOW TRIGGERS")
    triggers = source_cursor.fetchall()

    if not triggers:
        log_message(logger, "    - 未发现触发器。")
        return

    for trigger in triggers:
        trigger_name = trigger['Trigger']
        log_message(logger, f"    - 正在处理触发器: `{trigger_name}`")
        
        # SHOW CREATE TRIGGER 不可靠，我们手动构建
        event = trigger['Event']
        table = trigger['Table']
        timing = trigger['Timing']
        statement = trigger['Statement']
        
        # 移除 DEFINER
        statement = re.sub(r'DEFINER=`[^`]+`@`[^`]+`\s', '', statement, flags=re.IGNORECASE)

        sql = f"CREATE TRIGGER `{trigger_name}` {timing} {event} ON `{table}` FOR EACH ROW {statement}"

        try:
            dest_cursor.execute(f"DROP TRIGGER IF EXISTS `{trigger_name}`")
            dest_cursor.execute(sql)
        except pymysql.MySQLError as e:
            log_message(logger, f"      [警告] 迁移触发器 `{trigger_name}` 失败: {e}")


def _migrate_all_events_for_db(source_cursor, dest_cursor, logger):
    """迁移数据库中的所有事件"""
    log_message(logger, "\n  -> 正在迁移事件...")
    try:
        source_cursor.execute("SHOW EVENTS")
        events = source_cursor.fetchall()
        
        if not events:
            log_message(logger, "    - 未发现事件。")
            return

        for event in events:
            event_name = event['Name']
            log_message(logger, f"    - 正在处理事件: `{event_name}`")
            
            # SHOW CREATE EVENT 不存在，需要手动构建，但极其复杂
            # 为简化，仅记录并提示用户手动迁移
            log_message(logger, f"      [警告] 事件 `{event_name}` 的自动迁移非常复杂，建议手动迁移。")
            
    except pymysql.MySQLError as e:
        if "EVENT" in str(e):
             log_message(logger, "    - 源或目标数据库可能未开启事件调度器 (event_scheduler)。跳过事件迁移。")
        else:
            log_message(logger, f"      [错误] 检查事件时出错: {e}") 