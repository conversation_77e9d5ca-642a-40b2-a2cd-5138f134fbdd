[17:21:37] 已从 db_config.json 加载配置。
[17:21:38] 
==============================================
[17:21:38]           开始执行迁移任务...           
[17:21:38] ==============================================
[17:21:38] 调试: 迁移任务类型 - full
[17:21:38] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:21:38] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:21:38] 步骤 1/2: 迁移数据和结构
[17:21:38] --- 开始迁移数据和结构 ---
[17:21:38] 连接源数据库...
[17:21:38] 调试: 连接参数 - host=*************, port=3306, user=root
[17:21:38] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:21:38] 连接目标数据库...
[17:21:38] 调试: 连接参数 - host=*************, port=3306, user=root
[17:21:38] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:21:38] 发现 16 个用户数据库待迁移: 10010, bing_db, censys, dingding_MSG, gi888, mydatabase, nft_nftid, nft_nftid_bak, nft_recent, ollama, ookvipacc, ookvipnft, pc528, taobao_db, tempmail, wemix_news
[17:21:38] 
>> 正在处理数据库: `10010`
[17:21:38]    - 发现 3 个表...
[17:21:38]      - 正在迁移表 `processed_numbers` (42870 行)
[17:21:40]        已迁移 10000/42870 行...
[17:21:41]        已迁移 20000/42870 行...
[17:21:41]        已迁移 30000/42870 行...
[17:21:42]        已迁移 40000/42870 行...
[17:21:42]      - 表 `processed_numbers` 迁移完成 (42870 行)
[17:21:42]      - 正在迁移表 `special_numbers` (41 行)
[17:21:42]      - 表 `special_numbers` 迁移完成 (41 行)
[17:21:42]      - 正在迁移表 `token_cache` (1 行)
[17:21:43]      - 表 `token_cache` 迁移完成 (1 行)
[17:21:43]    - 所有表迁移完成。
[17:21:43] 
>> 正在处理数据库: `bing_db`
[17:21:43]    - 该数据库为空，跳过。
[17:21:43] 
>> 正在处理数据库: `censys`
[17:21:43]    - 发现 1 个表...
[17:21:43]      - 正在迁移表 `data` (1 行)
[17:21:43]      - 表 `data` 迁移完成 (1 行)
[17:21:43]    - 所有表迁移完成。
[17:21:43] 
>> 正在处理数据库: `dingding_MSG`
[17:21:43]    - 发现 1 个表...
[17:21:43]      - 正在迁移表 `notification_records` (12 行)
[17:21:43]      - 表 `notification_records` 迁移完成 (12 行)
[17:21:43]    - 所有表迁移完成。
[17:21:43] 
>> 正在处理数据库: `gi888`
[17:21:43]    - 发现 2 个表...
[17:21:43]      - 正在迁移表 `data` (1755 行)
[17:21:43]      - 表 `data` 迁移完成 (1755 行)
[17:21:43]      - 正在迁移表 `phone_accounts` (1755 行)
[17:21:43]      - 表 `phone_accounts` 迁移完成 (1755 行)
[17:21:43]    - 所有表迁移完成。
[17:21:43] 
>> 正在处理数据库: `mydatabase`
[17:21:43]    - 发现 3 个表...
[17:21:43]      - 正在迁移表 `models` (6477 行)
[17:21:44]      - 表 `models` 迁移完成 (6477 行)
[17:21:44]      - 正在迁移表 `models_copy1` (1670 行)
[17:21:45]      - 表 `models_copy1` 迁移完成 (1670 行)
[17:21:45]      - 正在迁移表 `models_old` (6410 行)
[17:21:46]      - 表 `models_old` 迁移完成 (6410 行)
[17:21:46]    - 所有表迁移完成。
[17:21:46] 
>> 正在处理数据库: `nft_nftid`
[17:21:46]    - 发现 1 个表...
[17:21:46]      - 正在迁移表 `data` (98423 行)
[17:21:47]        已迁移 10000/98423 行...
[17:21:48]        已迁移 20000/98423 行...
[17:21:49]        已迁移 30000/98423 行...
[17:21:50]        已迁移 40000/98423 行...
[17:21:51]        已迁移 50000/98423 行...
[17:21:52]        已迁移 60000/98423 行...
[17:21:52]        已迁移 70000/98423 行...
[17:21:53]        已迁移 80000/98423 行...
[17:21:54]        已迁移 90000/98423 行...
[17:21:55]      - 表 `data` 迁移完成 (98423 行)
[17:21:55]    - 所有表迁移完成。
[17:21:55]    - 发现 1 个事件...
[17:21:55]    - 所有事件迁移完成。
[17:21:55] 
>> 正在处理数据库: `nft_nftid_bak`
[17:21:55]    - 发现 1 个表...
[17:21:56]      - 正在迁移表 `data` (98423 行)
[17:21:57]        已迁移 10000/98423 行...
[17:21:58]        已迁移 20000/98423 行...
[17:21:59]        已迁移 30000/98423 行...
[17:22:00]        已迁移 40000/98423 行...
[17:22:01]        已迁移 50000/98423 行...
[17:22:02]        已迁移 60000/98423 行...
[17:22:02]        已迁移 70000/98423 行...
[17:22:03]        已迁移 80000/98423 行...
[17:22:04]        已迁移 90000/98423 行...
[17:22:05]      - 表 `data` 迁移完成 (98423 行)
[17:22:05]    - 所有表迁移完成。
[17:22:05] 
>> 正在处理数据库: `nft_recent`
[17:22:05]    - 发现 1 个表...
[17:22:05]      - 正在迁移表 `data` (155473 行)
[17:22:07]        已迁移 10000/155473 行...
[17:22:08]        已迁移 20000/155473 行...
[17:22:09]        已迁移 30000/155473 行...
[17:22:10]        已迁移 40000/155473 行...
[17:22:11]        已迁移 50000/155473 行...
[17:22:12]        已迁移 60000/155473 行...
[17:22:13]        已迁移 70000/155473 行...
[17:22:14]        已迁移 80000/155473 行...
[17:22:15]        已迁移 90000/155473 行...
[17:22:16]        已迁移 100000/155473 行...
[17:22:17]        已迁移 110000/155473 行...
[17:22:18]        已迁移 120000/155473 行...
[17:22:19]        已迁移 130000/155473 行...
[17:22:20]        已迁移 140000/155473 行...
[17:22:21]        已迁移 150000/155473 行...
[17:22:22]      - 表 `data` 迁移完成 (155473 行)
[17:22:22]    - 所有表迁移完成。
[17:22:22] 
>> 正在处理数据库: `ollama`
[17:22:22]    - 该数据库为空，跳过。
[17:22:22] 
>> 正在处理数据库: `ookvipacc`
[17:22:22]    - 发现 1 个表...
[17:22:22]      - 正在迁移表 `Account` (3 行)
[17:22:22]      - 表 `Account` 迁移完成 (3 行)
[17:22:22]    - 所有表迁移完成。
[17:22:22] 
>> 正在处理数据库: `ookvipnft`
[17:22:22]    - 发现 1 个表...
[17:22:22]      - 正在迁移表 `data` (1855 行)
[17:22:22]      - 表 `data` 迁移完成 (1855 行)
[17:22:22]    - 所有表迁移完成。
[17:22:22] 
>> 正在处理数据库: `pc528`
[17:22:23]    - 发现 3 个表...
[17:22:23]      - 正在迁移表 `access_logs` (1091 行)
[17:22:23]      - 表 `access_logs` 迁移完成 (1091 行)
[17:22:24]      - 正在迁移表 `articles` (47 行)
[17:22:24]      - 表 `articles` 迁移完成 (47 行)
[17:22:24]      - 正在迁移表 `auth_keys` (76 行)
[17:22:24]      - 表 `auth_keys` 迁移完成 (76 行)
[17:22:24]    - 所有表迁移完成。
[17:22:24] 
>> 正在处理数据库: `taobao_db`
[17:22:24]    - 发现 3 个表...
[17:22:24]      - 正在迁移表 `cookies` (1 行)
[17:22:24]      - 表 `cookies` 迁移完成 (1 行)
[17:22:24]      - 表 `monitor_tasks` 为空，跳过数据迁移
[17:22:25]      - 正在迁移表 `taobao_data` (536 行)
[17:22:25]      - 表 `taobao_data` 迁移完成 (536 行)
[17:22:25]    - 所有表迁移完成。
[17:22:25] 
>> 正在处理数据库: `tempmail`
[17:22:25]    - 发现 2 个表...
[17:22:25]      - 正在迁移表 `email_history` (46 行)
[17:22:25]      - 表 `email_history` 迁移完成 (46 行)
[17:22:25]      - 正在迁移表 `email_providers` (35 行)
[17:22:25]      - 表 `email_providers` 迁移完成 (35 行)
[17:22:25]    - 所有表迁移完成。
[17:22:25] 
>> 正在处理数据库: `wemix_news`
[17:22:25]    - 发现 1 个表...
[17:22:25]      - 正在迁移表 `wemix_news` (2534 行)
[17:22:26]      - 表 `wemix_news` 迁移完成 (2534 行)
[17:22:26]    - 所有表迁移完成。
[17:22:26] 
--- 所有数据和结构迁移成功！ ---
[17:22:26] 所有数据库连接已关闭。
[17:22:26] 
步骤 2/2: 迁移用户和权限
[17:22:26] --- 开始迁移用户和权限 ---
[17:22:26] 连接源数据库...
[17:22:26] 调试: 连接参数 - host=*************, port=3306, user=root
[17:22:26] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:22:26] 调试: 错误详情 - errno: -1, sqlstate: None
[17:22:26] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:22:26] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:24:13] 已从 db_config.json 加载配置。
[17:24:15] 
==============================================
[17:24:15]           开始执行迁移任务...           
[17:24:15] ==============================================
[17:24:15] 调试: 迁移任务类型 - full
[17:24:15] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:24:15] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:24:15] 步骤 1/2: 迁移数据和结构
[17:24:15] --- 开始迁移数据和结构 ---
[17:24:15] 连接源数据库...
[17:24:15] 调试: 连接参数 - host=*************, port=3306, user=root
[17:24:15] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:24:15] 连接目标数据库...
[17:24:15] 调试: 连接参数 - host=*************, port=3306, user=root
[17:24:15] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:24:15] 发现 16 个用户数据库待迁移: 10010, bing_db, censys, dingding_MSG, gi888, mydatabase, nft_nftid, nft_nftid_bak, nft_recent, ollama, ookvipacc, ookvipnft, pc528, taobao_db, tempmail, wemix_news
[17:24:15] 
>> 正在处理数据库: `10010`
[17:24:15]    - 发现 3 个表...
[17:24:16]      - 正在迁移表 `processed_numbers` (42870 行)
[17:24:17]        已迁移 10000/42870 行...
[17:24:18]        已迁移 20000/42870 行...
[17:24:19]        已迁移 30000/42870 行...
[17:24:19]        已迁移 40000/42870 行...
[17:24:20]      - 表 `processed_numbers` 迁移完成 (42870 行)
[17:24:20]      - 正在迁移表 `special_numbers` (41 行)
[17:24:20]      - 表 `special_numbers` 迁移完成 (41 行)
[17:24:20]      - 正在迁移表 `token_cache` (1 行)
[17:24:20]      - 表 `token_cache` 迁移完成 (1 行)
[17:24:20]    - 所有表迁移完成。
[17:24:20] 
>> 正在处理数据库: `bing_db`
[17:24:20]    - 该数据库为空，跳过。
[17:24:20] 
>> 正在处理数据库: `censys`
[17:24:20]    - 发现 1 个表...
[17:24:20]      - 正在迁移表 `data` (1 行)
[17:24:20]      - 表 `data` 迁移完成 (1 行)
[17:24:20]    - 所有表迁移完成。
[17:24:20] 
>> 正在处理数据库: `dingding_MSG`
[17:24:20]    - 发现 1 个表...
[17:24:20]      - 正在迁移表 `notification_records` (12 行)
[17:24:20]      - 表 `notification_records` 迁移完成 (12 行)
[17:24:20]    - 所有表迁移完成。
[17:24:20] 
>> 正在处理数据库: `gi888`
[17:24:20]    - 发现 2 个表...
[17:24:20]      - 正在迁移表 `data` (1755 行)
[17:24:20]      - 表 `data` 迁移完成 (1755 行)
[17:24:20]      - 正在迁移表 `phone_accounts` (1755 行)
[17:24:21]      - 表 `phone_accounts` 迁移完成 (1755 行)
[17:24:21]    - 所有表迁移完成。
[17:24:21] 
>> 正在处理数据库: `mydatabase`
[17:24:21]    - 发现 3 个表...
[17:24:21]      - 正在迁移表 `models` (6477 行)
[17:24:22]      - 表 `models` 迁移完成 (6477 行)
[17:24:22]      - 正在迁移表 `models_copy1` (1670 行)
[17:24:22]      - 表 `models_copy1` 迁移完成 (1670 行)
[17:24:22]      - 正在迁移表 `models_old` (6410 行)
[17:24:23]      - 表 `models_old` 迁移完成 (6410 行)
[17:24:23]    - 所有表迁移完成。
[17:24:23] 
>> 正在处理数据库: `nft_nftid`
[17:24:23]    - 发现 1 个表...
[17:24:23]      - 正在迁移表 `data` (98423 行)
[17:24:25]        已迁移 10000/98423 行...
[17:24:25]        已迁移 20000/98423 行...
[17:24:26]        已迁移 30000/98423 行...
[17:24:27]        已迁移 40000/98423 行...
[17:24:28]        已迁移 50000/98423 行...
[17:24:29]        已迁移 60000/98423 行...
[17:24:30]        已迁移 70000/98423 行...
[17:24:31]        已迁移 80000/98423 行...
[17:24:32]        已迁移 90000/98423 行...
[17:24:32]      - 表 `data` 迁移完成 (98423 行)
[17:24:32]    - 所有表迁移完成。
[17:24:32]    - 发现 1 个事件...
[17:24:32]    - 所有事件迁移完成。
[17:24:32] 
>> 正在处理数据库: `nft_nftid_bak`
[17:24:33]    - 发现 1 个表...
[17:24:33]      - 正在迁移表 `data` (98423 行)
[17:24:34]        已迁移 10000/98423 行...
[17:24:35]        已迁移 20000/98423 行...
[17:24:36]        已迁移 30000/98423 行...
[17:24:37]        已迁移 40000/98423 行...
[17:24:38]        已迁移 50000/98423 行...
[17:24:39]        已迁移 60000/98423 行...
[17:24:40]        已迁移 70000/98423 行...
[17:24:40]        已迁移 80000/98423 行...
[17:24:41]        已迁移 90000/98423 行...
[17:24:42]      - 表 `data` 迁移完成 (98423 行)
[17:24:42]    - 所有表迁移完成。
[17:24:42] 
>> 正在处理数据库: `nft_recent`
[17:24:42]    - 发现 1 个表...
[17:24:42]      - 正在迁移表 `data` (155473 行)
[17:24:44]        已迁移 10000/155473 行...
[17:24:45]        已迁移 20000/155473 行...
[17:24:46]        已迁移 30000/155473 行...
[17:24:47]        已迁移 40000/155473 行...
[17:24:48]        已迁移 50000/155473 行...
[17:24:49]        已迁移 60000/155473 行...
[17:24:50]        已迁移 70000/155473 行...
[17:24:51]        已迁移 80000/155473 行...
[17:24:52]        已迁移 90000/155473 行...
[17:24:53]        已迁移 100000/155473 行...
[17:24:54]        已迁移 110000/155473 行...
[17:24:55]        已迁移 120000/155473 行...
[17:24:56]        已迁移 130000/155473 行...
[17:24:57]        已迁移 140000/155473 行...
[17:24:58]        已迁移 150000/155473 行...
[17:24:59]      - 表 `data` 迁移完成 (155473 行)
[17:24:59]    - 所有表迁移完成。
[17:24:59] 
>> 正在处理数据库: `ollama`
[17:24:59]    - 该数据库为空，跳过。
[17:24:59] 
>> 正在处理数据库: `ookvipacc`
[17:24:59]    - 发现 1 个表...
[17:24:59]      - 正在迁移表 `Account` (3 行)
[17:24:59]      - 表 `Account` 迁移完成 (3 行)
[17:24:59]    - 所有表迁移完成。
[17:24:59] 
>> 正在处理数据库: `ookvipnft`
[17:24:59]    - 发现 1 个表...
[17:24:59]      - 正在迁移表 `data` (1855 行)
[17:24:59]      - 表 `data` 迁移完成 (1855 行)
[17:24:59]    - 所有表迁移完成。
[17:24:59] 
>> 正在处理数据库: `pc528`
[17:24:59]    - 发现 3 个表...
[17:25:00]      - 正在迁移表 `access_logs` (1091 行)
[17:25:00]      - 表 `access_logs` 迁移完成 (1091 行)
[17:25:00]      - 正在迁移表 `articles` (47 行)
[17:25:00]      - 表 `articles` 迁移完成 (47 行)
[17:25:01]      - 正在迁移表 `auth_keys` (76 行)
[17:25:01]      - 表 `auth_keys` 迁移完成 (76 行)
[17:25:01]    - 所有表迁移完成。
[17:25:01] 
>> 正在处理数据库: `taobao_db`
[17:25:01]    - 发现 3 个表...
[17:25:01]      - 正在迁移表 `cookies` (1 行)
[17:25:01]      - 表 `cookies` 迁移完成 (1 行)
[17:25:01]      - 表 `monitor_tasks` 为空，跳过数据迁移
[17:25:01]      - 正在迁移表 `taobao_data` (536 行)
[17:25:02]      - 表 `taobao_data` 迁移完成 (536 行)
[17:25:02]    - 所有表迁移完成。
[17:25:02] 
>> 正在处理数据库: `tempmail`
[17:25:02]    - 发现 2 个表...
[17:25:02]      - 正在迁移表 `email_history` (46 行)
[17:25:02]      - 表 `email_history` 迁移完成 (46 行)
[17:25:02]      - 正在迁移表 `email_providers` (35 行)
[17:25:02]      - 表 `email_providers` 迁移完成 (35 行)
[17:25:02]    - 所有表迁移完成。
[17:25:02] 
>> 正在处理数据库: `wemix_news`
[17:25:02]    - 发现 1 个表...
[17:25:02]      - 正在迁移表 `wemix_news` (2534 行)
[17:25:03]      - 表 `wemix_news` 迁移完成 (2534 行)
[17:25:03]    - 所有表迁移完成。
[17:25:03] 
--- 所有数据和结构迁移成功！ ---
[17:25:03] 所有数据库连接已关闭。
[17:25:03] 
步骤 2/2: 迁移用户和权限
[17:25:03] --- 开始迁移用户和权限 ---
[17:25:03] 连接源数据库...
[17:25:03] 调试: 连接参数 - host=*************, port=3306, user=root
[17:25:03] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:25:03] 调试: 错误详情 - errno: -1, sqlstate: None
[17:25:03] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:25:03] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:26:59] 已从 db_config.json 加载配置。
[17:27:05] 
==============================================
[17:27:05]           开始执行迁移任务...           
[17:27:05] ==============================================
[17:27:05] 调试: 迁移任务类型 - users
[17:27:05] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:27:05] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:27:05] --- 开始迁移用户和权限 ---
[17:27:05] 连接源数据库...
[17:27:05] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:05] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:27:05] 发现 2 个非系统用户待迁移。
[17:27:05]   -> 正在处理用户: 'gi'@'%'
[17:27:05]   -> 正在处理用户: 'taobao'@'%'
[17:27:05] 
模式: 直接在目标数据库执行命令...
[17:27:05] 连接目标数据库...
[17:27:05] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:05] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:27:05] 所有用户和权限已直接在目标数据库上创建/更新。
[17:27:05] 
--- 用户和权限迁移任务完成！ ---
[17:27:05] 
🎉🎉🎉 所有任务成功完成！ 🎉🎉🎉
[17:27:18] 
==============================================
[17:27:18]           开始执行迁移任务...           
[17:27:18] ==============================================
[17:27:18] 调试: 迁移任务类型 - full
[17:27:18] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:27:18] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:27:18] 步骤 1/2: 迁移数据和结构
[17:27:18] --- 开始迁移数据和结构 ---
[17:27:18] 连接源数据库...
[17:27:18] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:18] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:18] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:18] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:27:18] 所有数据库连接已关闭。
[17:27:18] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:27:20] 
==============================================
[17:27:20]           开始执行迁移任务...           
[17:27:20] ==============================================
[17:27:20] 调试: 迁移任务类型 - full
[17:27:20] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:27:20] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:27:20] 步骤 1/2: 迁移数据和结构
[17:27:20] --- 开始迁移数据和结构 ---
[17:27:20] 连接源数据库...
[17:27:20] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:20] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:20] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:20] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:27:20] 所有数据库连接已关闭。
[17:27:20] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:27:24] 
==============================================
[17:27:24]           开始执行迁移任务...           
[17:27:24] ==============================================
[17:27:24] 调试: 迁移任务类型 - full
[17:27:24] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:27:24] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:27:24] 步骤 1/2: 迁移数据和结构
[17:27:24] --- 开始迁移数据和结构 ---
[17:27:24] 连接源数据库...
[17:27:24] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:24] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:24] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:24] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:27:24] 所有数据库连接已关闭。
[17:27:24] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:27:28] 正在测试 source 数据库连接...
[17:27:28] 正在测试连接到 *************...
[17:27:28] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:28] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:28] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:28] 连接失败: Character set 'utf8' unsupported
[17:27:31] 正在测试 dest 数据库连接...
[17:27:31] 正在测试连接到 *************...
[17:27:31] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:31] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:27:31] 连接成功！
[17:27:33] 正在测试 source 数据库连接...
[17:27:33] 正在测试连接到 *************...
[17:27:33] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:33] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:33] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:33] 连接失败: Character set 'utf8' unsupported
[17:27:34] 正在测试 source 数据库连接...
[17:27:34] 正在测试连接到 *************...
[17:27:34] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:34] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:34] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:34] 连接失败: Character set 'utf8' unsupported
[17:27:35] 正在测试 source 数据库连接...
[17:27:35] 正在测试连接到 *************...
[17:27:35] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:35] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:35] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:35] 连接失败: Character set 'utf8' unsupported
[17:27:35] 正在测试 source 数据库连接...
[17:27:35] 正在测试连接到 *************...
[17:27:35] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:35] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:35] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:35] 连接失败: Character set 'utf8' unsupported
[17:27:36] 正在测试 source 数据库连接...
[17:27:36] 正在测试连接到 *************...
[17:27:36] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:36] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:36] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:36] 连接失败: Character set 'utf8' unsupported
[17:27:36] 正在测试 source 数据库连接...
[17:27:36] 正在测试连接到 *************...
[17:27:36] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:36] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:36] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:36] 连接失败: Character set 'utf8' unsupported
[17:27:36] 正在测试 source 数据库连接...
[17:27:36] 正在测试连接到 *************...
[17:27:36] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:36] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:36] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:36] 连接失败: Character set 'utf8' unsupported
[17:27:42] 
==============================================
[17:27:42]           开始执行迁移任务...           
[17:27:42] ==============================================
[17:27:42] 调试: 迁移任务类型 - full
[17:27:42] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:27:42] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:27:42] 步骤 1/2: 迁移数据和结构
[17:27:42] --- 开始迁移数据和结构 ---
[17:27:42] 连接源数据库...
[17:27:42] 调试: 连接参数 - host=*************, port=3306, user=root
[17:27:42] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:27:42] 调试: 错误详情 - errno: -1, sqlstate: None
[17:27:42] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:27:42] 所有数据库连接已关闭。
[17:27:42] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:28:07] 已从 db_config.json 加载配置。
[17:28:09] 
==============================================
[17:28:09]           开始执行迁移任务...           
[17:28:09] ==============================================
[17:28:09] 调试: 迁移任务类型 - full
[17:28:09] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:28:09] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:28:09] 步骤 1/2: 迁移数据和结构
[17:28:09] --- 开始迁移数据和结构 ---
[17:28:09] 连接源数据库...
[17:28:09] 调试: 连接参数 - host=*************, port=3306, user=root
[17:28:09] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:28:09] 连接目标数据库...
[17:28:09] 调试: 连接参数 - host=*************, port=3306, user=root
[17:28:09] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:28:09] 发现 16 个用户数据库待迁移: 10010, bing_db, censys, dingding_MSG, gi888, mydatabase, nft_nftid, nft_nftid_bak, nft_recent, ollama, ookvipacc, ookvipnft, pc528, taobao_db, tempmail, wemix_news
[17:28:09] 
>> 正在处理数据库: `10010`
[17:28:09]    - 发现 3 个表...
[17:28:09]      - 正在迁移表 `processed_numbers` (42870 行)
[17:28:11]        已迁移 10000/42870 行...
[17:28:11]        已迁移 20000/42870 行...
[17:28:12]        已迁移 30000/42870 行...
[17:28:13]        已迁移 40000/42870 行...
[17:28:13]      - 表 `processed_numbers` 迁移完成 (42870 行)
[17:28:13]      - 正在迁移表 `special_numbers` (41 行)
[17:28:13]      - 表 `special_numbers` 迁移完成 (41 行)
[17:28:13]      - 正在迁移表 `token_cache` (1 行)
[17:28:13]      - 表 `token_cache` 迁移完成 (1 行)
[17:28:13]    - 所有表迁移完成。
[17:28:13] 
>> 正在处理数据库: `bing_db`
[17:28:13]    - 该数据库为空，跳过。
[17:28:13] 
>> 正在处理数据库: `censys`
[17:28:13]    - 发现 1 个表...
[17:28:13]      - 正在迁移表 `data` (1 行)
[17:28:13]      - 表 `data` 迁移完成 (1 行)
[17:28:13]    - 所有表迁移完成。
[17:28:13] 
>> 正在处理数据库: `dingding_MSG`
[17:28:13]    - 发现 1 个表...
[17:28:13]      - 正在迁移表 `notification_records` (12 行)
[17:28:13]      - 表 `notification_records` 迁移完成 (12 行)
[17:28:13]    - 所有表迁移完成。
[17:28:13] 
>> 正在处理数据库: `gi888`
[17:28:13]    - 发现 2 个表...
[17:28:13]      - 正在迁移表 `data` (1755 行)
[17:28:14]      - 表 `data` 迁移完成 (1755 行)
[17:28:14]      - 正在迁移表 `phone_accounts` (1755 行)
[17:28:14]      - 表 `phone_accounts` 迁移完成 (1755 行)
[17:28:14]    - 所有表迁移完成。
[17:28:14] 
>> 正在处理数据库: `mydatabase`
[17:28:14]    - 发现 3 个表...
[17:28:14]      - 正在迁移表 `models` (6477 行)
[17:28:15]      - 表 `models` 迁移完成 (6477 行)
[17:28:15]      - 正在迁移表 `models_copy1` (1670 行)
[17:28:15]      - 表 `models_copy1` 迁移完成 (1670 行)
[17:28:15]      - 正在迁移表 `models_old` (6410 行)
[17:28:16]      - 表 `models_old` 迁移完成 (6410 行)
[17:28:16]    - 所有表迁移完成。
[17:28:16] 
>> 正在处理数据库: `nft_nftid`
[17:28:16]    - 发现 1 个表...
[17:28:17]      - 正在迁移表 `data` (98423 行)
[17:28:18]        已迁移 10000/98423 行...
[17:28:19]        已迁移 20000/98423 行...
[17:28:19]        已迁移 30000/98423 行...
[17:28:20]        已迁移 40000/98423 行...
[17:28:21]        已迁移 50000/98423 行...
[17:28:22]        已迁移 60000/98423 行...
[17:28:23]        已迁移 70000/98423 行...
[17:28:24]        已迁移 80000/98423 行...
[17:28:24]        已迁移 90000/98423 行...
[17:28:25]      - 表 `data` 迁移完成 (98423 行)
[17:28:25]    - 所有表迁移完成。
[17:28:25]    - 发现 1 个事件...
[17:28:25]    - 所有事件迁移完成。
[17:28:25] 
>> 正在处理数据库: `nft_nftid_bak`
[17:28:25]    - 发现 1 个表...
[17:28:26]      - 正在迁移表 `data` (98423 行)
[17:28:27]        已迁移 10000/98423 行...
[17:28:28]        已迁移 20000/98423 行...
[17:28:29]        已迁移 30000/98423 行...
[17:28:30]        已迁移 40000/98423 行...
[17:28:31]        已迁移 50000/98423 行...
[17:28:32]        已迁移 60000/98423 行...
[17:28:32]        已迁移 70000/98423 行...
[17:28:33]        已迁移 80000/98423 行...
[17:28:34]        已迁移 90000/98423 行...
[17:28:35]      - 表 `data` 迁移完成 (98423 行)
[17:28:35]    - 所有表迁移完成。
[17:28:35] 
>> 正在处理数据库: `nft_recent`
[17:28:35]    - 发现 1 个表...
[17:28:35]      - 正在迁移表 `data` (155473 行)
[17:28:37]        已迁移 10000/155473 行...
[17:28:38]        已迁移 20000/155473 行...
[17:28:39]        已迁移 30000/155473 行...
[17:28:40]        已迁移 40000/155473 行...
[17:28:41]        已迁移 50000/155473 行...
[17:28:42]        已迁移 60000/155473 行...
[17:28:43]        已迁移 70000/155473 行...
[17:28:44]        已迁移 80000/155473 行...
[17:28:45]        已迁移 90000/155473 行...
[17:28:46]        已迁移 100000/155473 行...
[17:28:47]        已迁移 110000/155473 行...
[17:28:48]        已迁移 120000/155473 行...
[17:28:49]        已迁移 130000/155473 行...
[17:28:50]        已迁移 140000/155473 行...
[17:28:51]        已迁移 150000/155473 行...
[17:28:52]      - 表 `data` 迁移完成 (155473 行)
[17:28:52]    - 所有表迁移完成。
[17:28:52] 
>> 正在处理数据库: `ollama`
[17:28:52]    - 该数据库为空，跳过。
[17:28:52] 
>> 正在处理数据库: `ookvipacc`
[17:28:52]    - 发现 1 个表...
[17:28:52]      - 正在迁移表 `Account` (3 行)
[17:28:52]      - 表 `Account` 迁移完成 (3 行)
[17:28:52]    - 所有表迁移完成。
[17:28:52] 
>> 正在处理数据库: `ookvipnft`
[17:28:52]    - 发现 1 个表...
[17:28:52]      - 正在迁移表 `data` (1855 行)
[17:28:52]      - 表 `data` 迁移完成 (1855 行)
[17:28:52]    - 所有表迁移完成。
[17:28:52] 
>> 正在处理数据库: `pc528`
[17:28:52]    - 发现 3 个表...
[17:28:53]      - 正在迁移表 `access_logs` (1091 行)
[17:28:53]      - 表 `access_logs` 迁移完成 (1091 行)
[17:28:54]      - 正在迁移表 `articles` (47 行)
[17:28:54]      - 表 `articles` 迁移完成 (47 行)
[17:28:54]      - 正在迁移表 `auth_keys` (76 行)
[17:28:54]      - 表 `auth_keys` 迁移完成 (76 行)
[17:28:54]    - 所有表迁移完成。
[17:28:54] 
>> 正在处理数据库: `taobao_db`
[17:28:54]    - 发现 3 个表...
[17:28:54]      - 正在迁移表 `cookies` (1 行)
[17:28:54]      - 表 `cookies` 迁移完成 (1 行)
[17:28:54]      - 表 `monitor_tasks` 为空，跳过数据迁移
[17:28:54]      - 正在迁移表 `taobao_data` (536 行)
[17:28:55]      - 表 `taobao_data` 迁移完成 (536 行)
[17:28:55]    - 所有表迁移完成。
[17:28:55] 
>> 正在处理数据库: `tempmail`
[17:28:55]    - 发现 2 个表...
[17:28:55]      - 正在迁移表 `email_history` (46 行)
[17:28:55]      - 表 `email_history` 迁移完成 (46 行)
[17:28:55]      - 正在迁移表 `email_providers` (35 行)
[17:28:55]      - 表 `email_providers` 迁移完成 (35 行)
[17:28:55]    - 所有表迁移完成。
[17:28:55] 
>> 正在处理数据库: `wemix_news`
[17:28:55]    - 发现 1 个表...
[17:28:55]      - 正在迁移表 `wemix_news` (2535 行)
[17:28:56]      - 表 `wemix_news` 迁移完成 (2535 行)
[17:28:56]    - 所有表迁移完成。
[17:28:56] 
--- 所有数据和结构迁移成功！ ---
[17:28:56] 所有数据库连接已关闭。
[17:28:56] 
步骤 2/2: 迁移用户和权限
[17:28:56] --- 开始迁移用户和权限 ---
[17:28:56] 连接源数据库...
[17:28:56] 调试: 连接参数 - host=*************, port=3306, user=root
[17:28:56] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:28:56] 调试: 错误详情 - errno: -1, sqlstate: None
[17:28:56] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:28:56] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:36:23] 
==============================================
[17:36:23]           开始执行迁移任务...           
[17:36:23] ==============================================
[17:36:23] 调试: 迁移任务类型 - users
[17:36:23] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:36:23] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:36:23] --- 开始迁移用户和权限 ---
[17:36:23] 连接源数据库...
[17:36:23] 调试: 连接参数 - host=*************, port=3306, user=root
[17:36:23] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:36:23] 调试: 错误详情 - errno: -1, sqlstate: None
[17:36:23] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:36:23] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:36:29] 
==============================================
[17:36:29]           开始执行迁移任务...           
[17:36:29] ==============================================
[17:36:29] 调试: 迁移任务类型 - users
[17:36:29] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:36:29] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:36:29] --- 开始迁移用户和权限 ---
[17:36:29] 连接源数据库...
[17:36:29] 调试: 连接参数 - host=*************, port=3306, user=root
[17:36:29] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:36:29] 调试: 错误详情 - errno: -1, sqlstate: None
[17:36:29] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:36:29] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:36:30] 
==============================================
[17:36:30]           开始执行迁移任务...           
[17:36:30] ==============================================
[17:36:30] 调试: 迁移任务类型 - users
[17:36:30] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:36:30] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:36:30] --- 开始迁移用户和权限 ---
[17:36:30] 连接源数据库...
[17:36:30] 调试: 连接参数 - host=*************, port=3306, user=root
[17:36:30] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:36:30] 调试: 错误详情 - errno: -1, sqlstate: None
[17:36:30] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:36:30] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:36:31] 
==============================================
[17:36:31]           开始执行迁移任务...           
[17:36:31] ==============================================
[17:36:31] 调试: 迁移任务类型 - users
[17:36:31] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:36:31] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:36:31] --- 开始迁移用户和权限 ---
[17:36:31] 连接源数据库...
[17:36:31] 调试: 连接参数 - host=*************, port=3306, user=root
[17:36:31] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:36:31] 调试: 错误详情 - errno: -1, sqlstate: None
[17:36:31] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:36:31] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:36:34] 已从 db_config.json 加载配置。
[17:36:37] 
==============================================
[17:36:37]           开始执行迁移任务...           
[17:36:37] ==============================================
[17:36:37] 调试: 迁移任务类型 - users
[17:36:37] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:36:37] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:36:37] --- 开始迁移用户和权限 ---
[17:36:37] 连接源数据库...
[17:36:37] 调试: 连接参数 - host=*************, port=3306, user=root
[17:36:37] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:36:37] 发现 2 个非系统用户待迁移。
[17:36:37]   -> 正在处理用户: 'gi'@'%'
[17:36:37]   -> 正在处理用户: 'taobao'@'%'
[17:36:37] 
模式: 直接在目标数据库执行命令...
[17:36:37] 连接目标数据库...
[17:36:37] 调试: 连接参数 - host=*************, port=3306, user=root
[17:36:37] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:36:37] 所有用户和权限已直接在目标数据库上创建/更新。
[17:36:37] 
--- 用户和权限迁移任务完成！ ---
[17:36:37] 
🎉🎉🎉 所有任务成功完成！ 🎉🎉🎉
[17:41:48] 已从 db_config.json 加载配置。
[17:41:53] 已从 db_config.json 加载配置。
[17:41:56] 正在测试 source 数据库连接...
[17:41:56] 正在测试连接到 *************...
[17:41:56] 调试: 连接参数 - host=*************, port=3306, user=root
[17:41:56] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:41:56] 连接成功！
[17:41:57] 正在测试 dest 数据库连接...
[17:41:57] 正在测试连接到 *************...
[17:41:57] 调试: 连接参数 - host=*************, port=3306, user=root
[17:41:57] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:41:57] 连接成功！
[17:42:00] 
==============================================
[17:42:00]           开始执行迁移任务...           
[17:42:00] ==============================================
[17:42:00] 调试: 迁移任务类型 - full
[17:42:00] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:42:00] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:42:00] 步骤 1/2: 迁移数据和结构
[17:42:00] --- 开始迁移数据和结构 ---
[17:42:00] 连接源数据库...
[17:42:00] 调试: 连接参数 - host=*************, port=3306, user=root
[17:42:00] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:42:00] 调试: 错误详情 - errno: -1, sqlstate: None
[17:42:00] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:42:00] 所有数据库连接已关闭。
[17:42:00] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:42:06] 
==============================================
[17:42:06]           开始执行迁移任务...           
[17:42:06] ==============================================
[17:42:06] 调试: 迁移任务类型 - full
[17:42:06] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:42:06] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:42:06] 步骤 1/2: 迁移数据和结构
[17:42:06] --- 开始迁移数据和结构 ---
[17:42:06] 连接源数据库...
[17:42:06] 调试: 连接参数 - host=*************, port=3306, user=root
[17:42:06] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:42:06] 调试: 错误详情 - errno: -1, sqlstate: None
[17:42:06] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:42:06] 所有数据库连接已关闭。
[17:42:06] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:42:11] 
==============================================
[17:42:11]           开始执行迁移任务...           
[17:42:11] ==============================================
[17:42:11] 调试: 迁移任务类型 - full
[17:42:11] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:42:11] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:42:11] 步骤 1/2: 迁移数据和结构
[17:42:11] --- 开始迁移数据和结构 ---
[17:42:11] 连接源数据库...
[17:42:11] 调试: 连接参数 - host=*************, port=3306, user=root
[17:42:11] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:42:11] 调试: 错误详情 - errno: -1, sqlstate: None
[17:42:11] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:42:11] 所有数据库连接已关闭。
[17:42:11] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:42:15] 已从 db_config.json 加载配置。
[17:42:17] 
==============================================
[17:42:17]           开始执行迁移任务...           
[17:42:17] ==============================================
[17:42:17] 调试: 迁移任务类型 - full
[17:42:17] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:42:17] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:42:17] 步骤 1/2: 迁移数据和结构
[17:42:17] --- 开始迁移数据和结构 ---
[17:42:17] 连接源数据库...
[17:42:17] 调试: 连接参数 - host=*************, port=3306, user=root
[17:42:17] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:42:17] 连接目标数据库...
[17:42:17] 调试: 连接参数 - host=*************, port=3306, user=root
[17:42:17] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:42:17] 发现 16 个用户数据库待迁移: 10010, bing_db, censys, dingding_MSG, gi888, mydatabase, nft_nftid, nft_nftid_bak, nft_recent, ollama, ookvipacc, ookvipnft, pc528, taobao_db, tempmail, wemix_news
[17:42:17] 
>> 正在处理数据库: `10010`
[17:42:17]    - 发现 3 个表...
[17:42:17]      - 正在迁移表 `processed_numbers` (42870 行)
[17:42:19]        已迁移 10000/42870 行...
[17:42:19]        已迁移 20000/42870 行...
[17:42:20]        已迁移 30000/42870 行...
[17:42:20]        已迁移 40000/42870 行...
[17:42:21]      - 表 `processed_numbers` 迁移完成 (42870 行)
[17:42:21]      - 正在迁移表 `special_numbers` (41 行)
[17:42:21]      - 表 `special_numbers` 迁移完成 (41 行)
[17:42:21]      - 正在迁移表 `token_cache` (1 行)
[17:42:21]      - 表 `token_cache` 迁移完成 (1 行)
[17:42:21]    - 所有表迁移完成。
[17:42:21] 
>> 正在处理数据库: `bing_db`
[17:42:21]    - 该数据库为空，跳过。
[17:42:21] 
>> 正在处理数据库: `censys`
[17:42:21]    - 发现 1 个表...
[17:42:21]      - 正在迁移表 `data` (1 行)
[17:42:21]      - 表 `data` 迁移完成 (1 行)
[17:42:21]    - 所有表迁移完成。
[17:42:21] 
>> 正在处理数据库: `dingding_MSG`
[17:42:21]    - 发现 1 个表...
[17:42:21]      - 正在迁移表 `notification_records` (12 行)
[17:42:21]      - 表 `notification_records` 迁移完成 (12 行)
[17:42:21]    - 所有表迁移完成。
[17:42:21] 
>> 正在处理数据库: `gi888`
[17:42:21]    - 发现 2 个表...
[17:42:21]      - 正在迁移表 `data` (1755 行)
[17:42:21]      - 表 `data` 迁移完成 (1755 行)
[17:42:22]      - 正在迁移表 `phone_accounts` (1755 行)
[17:42:22]      - 表 `phone_accounts` 迁移完成 (1755 行)
[17:42:22]    - 所有表迁移完成。
[17:42:22] 
>> 正在处理数据库: `mydatabase`
[17:42:22]    - 发现 3 个表...
[17:42:22]      - 正在迁移表 `models` (6477 行)
[17:42:23]      - 表 `models` 迁移完成 (6477 行)
[17:42:23]      - 正在迁移表 `models_copy1` (1670 行)
[17:42:23]      - 表 `models_copy1` 迁移完成 (1670 行)
[17:42:23]      - 正在迁移表 `models_old` (6410 行)
[17:42:24]      - 表 `models_old` 迁移完成 (6410 行)
[17:42:24]    - 所有表迁移完成。
[17:42:24] 
>> 正在处理数据库: `nft_nftid`
[17:42:24]    - 发现 1 个表...
[17:42:24]      - 正在迁移表 `data` (98423 行)
[17:42:25]        已迁移 10000/98423 行...
[17:42:26]        已迁移 20000/98423 行...
[17:42:27]        已迁移 30000/98423 行...
[17:42:28]        已迁移 40000/98423 行...
[17:42:29]        已迁移 50000/98423 行...
[17:42:29]        已迁移 60000/98423 行...
[17:42:30]        已迁移 70000/98423 行...
[17:42:31]        已迁移 80000/98423 行...
[17:42:32]        已迁移 90000/98423 行...
[17:42:33]      - 表 `data` 迁移完成 (98423 行)
[17:42:33]    - 所有表迁移完成。
[17:42:33]    - 发现 1 个事件...
[17:42:33]    - 所有事件迁移完成。
[17:42:33] 
>> 正在处理数据库: `nft_nftid_bak`
[17:42:33]    - 发现 1 个表...
[17:42:33]      - 正在迁移表 `data` (98423 行)
[17:42:35]        已迁移 10000/98423 行...
[17:42:36]        已迁移 20000/98423 行...
[17:42:37]        已迁移 30000/98423 行...
[17:42:37]        已迁移 40000/98423 行...
[17:42:38]        已迁移 50000/98423 行...
[17:42:39]        已迁移 60000/98423 行...
[17:42:40]        已迁移 70000/98423 行...
[17:42:41]        已迁移 80000/98423 行...
[17:42:42]        已迁移 90000/98423 行...
[17:42:42]      - 表 `data` 迁移完成 (98423 行)
[17:42:42]    - 所有表迁移完成。
[17:42:42] 
>> 正在处理数据库: `nft_recent`
[17:42:43]    - 发现 1 个表...
[17:42:43]      - 正在迁移表 `data` (155473 行)
[17:42:44]        已迁移 10000/155473 行...
[17:42:46]        已迁移 20000/155473 行...
[17:42:46]        已迁移 30000/155473 行...
[17:42:47]        已迁移 40000/155473 行...
[17:42:48]        已迁移 50000/155473 行...
[17:42:49]        已迁移 60000/155473 行...
[17:42:50]        已迁移 70000/155473 行...
[17:42:51]        已迁移 80000/155473 行...
[17:42:52]        已迁移 90000/155473 行...
[17:42:53]        已迁移 100000/155473 行...
[17:42:54]        已迁移 110000/155473 行...
[17:42:55]        已迁移 120000/155473 行...
[17:42:57]        已迁移 130000/155473 行...
[17:42:58]        已迁移 140000/155473 行...
[17:42:59]        已迁移 150000/155473 行...
[17:42:59]      - 表 `data` 迁移完成 (155473 行)
[17:42:59]    - 所有表迁移完成。
[17:42:59] 
>> 正在处理数据库: `ollama`
[17:42:59]    - 该数据库为空，跳过。
[17:42:59] 
>> 正在处理数据库: `ookvipacc`
[17:42:59]    - 发现 1 个表...
[17:42:59]      - 正在迁移表 `Account` (3 行)
[17:42:59]      - 表 `Account` 迁移完成 (3 行)
[17:42:59]    - 所有表迁移完成。
[17:42:59] 
>> 正在处理数据库: `ookvipnft`
[17:42:59]    - 发现 1 个表...
[17:43:00]      - 正在迁移表 `data` (1855 行)
[17:43:00]      - 表 `data` 迁移完成 (1855 行)
[17:43:00]    - 所有表迁移完成。
[17:43:00] 
>> 正在处理数据库: `pc528`
[17:43:00]    - 发现 3 个表...
[17:43:00]      - 正在迁移表 `access_logs` (1091 行)
[17:43:01]      - 表 `access_logs` 迁移完成 (1091 行)
[17:43:01]      - 正在迁移表 `articles` (47 行)
[17:43:01]      - 表 `articles` 迁移完成 (47 行)
[17:43:01]      - 正在迁移表 `auth_keys` (76 行)
[17:43:01]      - 表 `auth_keys` 迁移完成 (76 行)
[17:43:01]    - 所有表迁移完成。
[17:43:01] 
>> 正在处理数据库: `taobao_db`
[17:43:01]    - 发现 3 个表...
[17:43:02]      - 正在迁移表 `cookies` (1 行)
[17:43:02]      - 表 `cookies` 迁移完成 (1 行)
[17:43:02]      - 表 `monitor_tasks` 为空，跳过数据迁移
[17:43:02]      - 正在迁移表 `taobao_data` (536 行)
[17:43:02]      - 表 `taobao_data` 迁移完成 (536 行)
[17:43:02]    - 所有表迁移完成。
[17:43:02] 
>> 正在处理数据库: `tempmail`
[17:43:02]    - 发现 2 个表...
[17:43:02]      - 正在迁移表 `email_history` (46 行)
[17:43:02]      - 表 `email_history` 迁移完成 (46 行)
[17:43:02]      - 正在迁移表 `email_providers` (35 行)
[17:43:02]      - 表 `email_providers` 迁移完成 (35 行)
[17:43:02]    - 所有表迁移完成。
[17:43:02] 
>> 正在处理数据库: `wemix_news`
[17:43:02]    - 发现 1 个表...
[17:43:03]      - 正在迁移表 `wemix_news` (2535 行)
[17:43:03]      - 表 `wemix_news` 迁移完成 (2535 行)
[17:43:03]    - 所有表迁移完成。
[17:43:03] 
--- 所有数据和结构迁移成功！ ---
[17:43:03] 所有数据库连接已关闭。
[17:43:03] 等待连接资源释放 (2秒)...
[17:43:05] 
步骤 2/2: 迁移用户和权限
[17:43:05] --- 开始迁移用户和权限 ---
[17:43:05] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb3
[17:43:05] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[17:43:05] 连接源数据库...
[17:43:05] 调试: 连接参数 - host=*************, port=3306, user=root
[17:43:05] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:43:05] 调试: 错误详情 - errno: -1, sqlstate: None
[17:43:05] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:43:05] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:44:17] 
==============================================
[17:44:17]           开始执行迁移任务...           
[17:44:17] ==============================================
[17:44:17] 调试: 迁移任务类型 - full
[17:44:17] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:44:17] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:44:17] 步骤 1/2: 迁移数据和结构
[17:44:17] --- 开始迁移数据和结构 ---
[17:44:17] 连接源数据库...
[17:44:17] 调试: 连接参数 - host=*************, port=3306, user=root
[17:44:17] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:44:17] 调试: 错误详情 - errno: -1, sqlstate: None
[17:44:17] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:44:17] 所有数据库连接已关闭。
[17:44:17] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:44:18] 
==============================================
[17:44:18]           开始执行迁移任务...           
[17:44:18] ==============================================
[17:44:18] 调试: 迁移任务类型 - full
[17:44:18] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:44:18] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:44:18] 步骤 1/2: 迁移数据和结构
[17:44:18] --- 开始迁移数据和结构 ---
[17:44:18] 连接源数据库...
[17:44:18] 调试: 连接参数 - host=*************, port=3306, user=root
[17:44:18] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:44:18] 调试: 错误详情 - errno: -1, sqlstate: None
[17:44:18] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:44:18] 所有数据库连接已关闭。
[17:44:18] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:44:19] 
==============================================
[17:44:19]           开始执行迁移任务...           
[17:44:19] ==============================================
[17:44:19] 调试: 迁移任务类型 - full
[17:44:19] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:44:19] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:44:19] 步骤 1/2: 迁移数据和结构
[17:44:19] --- 开始迁移数据和结构 ---
[17:44:19] 连接源数据库...
[17:44:19] 调试: 连接参数 - host=*************, port=3306, user=root
[17:44:19] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:44:19] 调试: 错误详情 - errno: -1, sqlstate: None
[17:44:19] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:44:19] 所有数据库连接已关闭。
[17:44:19] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:45:58] 已从 db_config.json 加载配置。
[17:46:00] 
==============================================
[17:46:00]           开始执行迁移任务...           
[17:46:00] ==============================================
[17:46:00] 调试: 迁移任务类型 - full
[17:46:00] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:46:00] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:46:00] 步骤 1/2: 迁移数据和结构
[17:46:00] --- 开始迁移数据和结构 ---
[17:46:00] 连接源数据库...
[17:46:00] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:46:00] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:46:00] 连接目标数据库...
[17:46:00] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[17:46:00] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:46:00] 发现 16 个用户数据库待迁移: 10010, bing_db, censys, dingding_MSG, gi888, mydatabase, nft_nftid, nft_nftid_bak, nft_recent, ollama, ookvipacc, ookvipnft, pc528, taobao_db, tempmail, wemix_news
[17:46:00] 
>> 正在处理数据库: `10010`
[17:46:00]    - 发现 3 个表...
[17:46:00]      - 正在迁移表 `processed_numbers` (42871 行)
[17:46:02]        已迁移 10000/42871 行...
[17:46:02]        已迁移 20000/42871 行...
[17:46:03]        已迁移 30000/42871 行...
[17:46:04]        已迁移 40000/42871 行...
[17:46:04]      - 表 `processed_numbers` 迁移完成 (42871 行)
[17:46:04]      - 正在迁移表 `special_numbers` (41 行)
[17:46:04]      - 表 `special_numbers` 迁移完成 (41 行)
[17:46:04]      - 正在迁移表 `token_cache` (1 行)
[17:46:04]      - 表 `token_cache` 迁移完成 (1 行)
[17:46:04]    - 所有表迁移完成。
[17:46:04] 
>> 正在处理数据库: `bing_db`
[17:46:04]    - 该数据库为空，跳过。
[17:46:04] 
>> 正在处理数据库: `censys`
[17:46:04]    - 发现 1 个表...
[17:46:04]      - 正在迁移表 `data` (1 行)
[17:46:04]      - 表 `data` 迁移完成 (1 行)
[17:46:04]    - 所有表迁移完成。
[17:46:04] 
>> 正在处理数据库: `dingding_MSG`
[17:46:04]    - 发现 1 个表...
[17:46:04]      - 正在迁移表 `notification_records` (12 行)
[17:46:04]      - 表 `notification_records` 迁移完成 (12 行)
[17:46:04]    - 所有表迁移完成。
[17:46:04] 
>> 正在处理数据库: `gi888`
[17:46:04]    - 发现 2 个表...
[17:46:04]      - 正在迁移表 `data` (1755 行)
[17:46:05]      - 表 `data` 迁移完成 (1755 行)
[17:46:05]      - 正在迁移表 `phone_accounts` (1755 行)
[17:46:05]      - 表 `phone_accounts` 迁移完成 (1755 行)
[17:46:05]    - 所有表迁移完成。
[17:46:05] 
>> 正在处理数据库: `mydatabase`
[17:46:05]    - 发现 3 个表...
[17:46:05]      - 正在迁移表 `models` (6477 行)
[17:46:06]      - 表 `models` 迁移完成 (6477 行)
[17:46:06]      - 正在迁移表 `models_copy1` (1670 行)
[17:46:06]      - 表 `models_copy1` 迁移完成 (1670 行)
[17:46:07]      - 正在迁移表 `models_old` (6410 行)
[17:46:07]      - 表 `models_old` 迁移完成 (6410 行)
[17:46:07]    - 所有表迁移完成。
[17:46:07] 
>> 正在处理数据库: `nft_nftid`
[17:46:07]    - 发现 1 个表...
[17:46:08]      - 正在迁移表 `data` (98423 行)
[17:46:09]        已迁移 10000/98423 行...
[17:46:10]        已迁移 20000/98423 行...
[17:46:10]        已迁移 30000/98423 行...
[17:46:11]        已迁移 40000/98423 行...
[17:46:12]        已迁移 50000/98423 行...
[17:46:13]        已迁移 60000/98423 行...
[17:46:14]        已迁移 70000/98423 行...
[17:46:15]        已迁移 80000/98423 行...
[17:46:15]        已迁移 90000/98423 行...
[17:46:16]      - 表 `data` 迁移完成 (98423 行)
[17:46:16]    - 所有表迁移完成。
[17:46:16]    - 发现 1 个事件...
[17:46:16]    - 所有事件迁移完成。
[17:46:16] 
>> 正在处理数据库: `nft_nftid_bak`
[17:46:16]    - 发现 1 个表...
[17:46:17]      - 正在迁移表 `data` (98423 行)
[17:46:18]        已迁移 10000/98423 行...
[17:46:19]        已迁移 20000/98423 行...
[17:46:20]        已迁移 30000/98423 行...
[17:46:21]        已迁移 40000/98423 行...
[17:46:22]        已迁移 50000/98423 行...
[17:46:23]        已迁移 60000/98423 行...
[17:46:24]        已迁移 70000/98423 行...
[17:46:24]        已迁移 80000/98423 行...
[17:46:25]        已迁移 90000/98423 行...
[17:46:26]      - 表 `data` 迁移完成 (98423 行)
[17:46:26]    - 所有表迁移完成。
[17:46:26] 
>> 正在处理数据库: `nft_recent`
[17:46:26]    - 发现 1 个表...
[17:46:26]      - 正在迁移表 `data` (155473 行)
[17:46:28]        已迁移 10000/155473 行...
[17:46:29]        已迁移 20000/155473 行...
[17:46:30]        已迁移 30000/155473 行...
[17:46:31]        已迁移 40000/155473 行...
[17:46:32]        已迁移 50000/155473 行...
[17:46:33]        已迁移 60000/155473 行...
[17:46:34]        已迁移 70000/155473 行...
[17:46:35]        已迁移 80000/155473 行...
[17:46:36]        已迁移 90000/155473 行...
[17:46:37]        已迁移 100000/155473 行...
[17:46:38]        已迁移 110000/155473 行...
[17:46:39]        已迁移 120000/155473 行...
[17:46:40]        已迁移 130000/155473 行...
[17:46:41]        已迁移 140000/155473 行...
[17:46:42]        已迁移 150000/155473 行...
[17:46:43]      - 表 `data` 迁移完成 (155473 行)
[17:46:43]    - 所有表迁移完成。
[17:46:43] 
>> 正在处理数据库: `ollama`
[17:46:43]    - 该数据库为空，跳过。
[17:46:43] 
>> 正在处理数据库: `ookvipacc`
[17:46:43]    - 发现 1 个表...
[17:46:43]      - 正在迁移表 `Account` (3 行)
[17:46:43]      - 表 `Account` 迁移完成 (3 行)
[17:46:43]    - 所有表迁移完成。
[17:46:43] 
>> 正在处理数据库: `ookvipnft`
[17:46:43]    - 发现 1 个表...
[17:46:43]      - 正在迁移表 `data` (1855 行)
[17:46:43]      - 表 `data` 迁移完成 (1855 行)
[17:46:43]    - 所有表迁移完成。
[17:46:43] 
>> 正在处理数据库: `pc528`
[17:46:43]    - 发现 3 个表...
[17:46:44]      - 正在迁移表 `access_logs` (1091 行)
[17:46:44]      - 表 `access_logs` 迁移完成 (1091 行)
[17:46:44]      - 正在迁移表 `articles` (47 行)
[17:46:44]      - 表 `articles` 迁移完成 (47 行)
[17:46:45]      - 正在迁移表 `auth_keys` (76 行)
[17:46:45]      - 表 `auth_keys` 迁移完成 (76 行)
[17:46:45]    - 所有表迁移完成。
[17:46:45] 
>> 正在处理数据库: `taobao_db`
[17:46:45]    - 发现 3 个表...
[17:46:45]      - 正在迁移表 `cookies` (1 行)
[17:46:45]      - 表 `cookies` 迁移完成 (1 行)
[17:46:45]      - 表 `monitor_tasks` 为空，跳过数据迁移
[17:46:45]      - 正在迁移表 `taobao_data` (536 行)
[17:46:45]      - 表 `taobao_data` 迁移完成 (536 行)
[17:46:45]    - 所有表迁移完成。
[17:46:45] 
>> 正在处理数据库: `tempmail`
[17:46:45]    - 发现 2 个表...
[17:46:45]      - 正在迁移表 `email_history` (46 行)
[17:46:46]      - 表 `email_history` 迁移完成 (46 行)
[17:46:46]      - 正在迁移表 `email_providers` (35 行)
[17:46:46]      - 表 `email_providers` 迁移完成 (35 行)
[17:46:46]    - 所有表迁移完成。
[17:46:46] 
>> 正在处理数据库: `wemix_news`
[17:46:46]    - 发现 1 个表...
[17:46:46]      - 正在迁移表 `wemix_news` (2535 行)
[17:46:47]      - 表 `wemix_news` 迁移完成 (2535 行)
[17:46:47]    - 所有表迁移完成。
[17:46:47] 
--- 所有数据和结构迁移成功！ ---
[17:46:47] 所有数据库连接已关闭。
[17:46:47] 等待连接资源释放 (2秒)...
[17:46:49] 
步骤 2/2: 迁移用户和权限
[17:46:49] --- 开始迁移用户和权限 ---
[17:46:49] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb3
[17:46:49] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[17:46:49] 连接源数据库...
[17:46:49] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:46:49] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:46:49] 调试: 错误详情 - errno: -1, sqlstate: None
[17:46:49] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:46:49] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:46:58] 已从 db_config.json 加载配置。
[17:47:00] 
==============================================
[17:47:00]           开始执行迁移任务...           
[17:47:00] ==============================================
[17:47:00] 调试: 迁移任务类型 - users
[17:47:00] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:47:00] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:47:00] --- 开始迁移用户和权限 ---
[17:47:00] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb3
[17:47:00] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[17:47:00] 连接源数据库...
[17:47:00] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:47:00] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:47:00] 发现 2 个非系统用户待迁移。
[17:47:00]   -> 正在处理用户: 'gi'@'%'
[17:47:00]   -> 正在处理用户: 'taobao'@'%'
[17:47:00] 
模式: 生成 user_migration.sql 文件...
[17:47:00] 成功！用户迁移脚本已生成: user_migration.sql
[17:47:00] 
--- 用户和权限迁移任务完成！ ---
[17:47:00] 
🎉🎉🎉 所有任务成功完成！ 🎉🎉🎉
[17:47:04] 
==============================================
[17:47:04]           开始执行迁移任务...           
[17:47:04] ==============================================
[17:47:04] 调试: 迁移任务类型 - data
[17:47:04] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:47:04] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:47:04] --- 开始迁移数据和结构 ---
[17:47:04] 连接源数据库...
[17:47:04] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:47:04] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:47:04] 连接目标数据库...
[17:47:04] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[17:47:04] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:47:04] 发现 16 个用户数据库待迁移: 10010, bing_db, censys, dingding_MSG, gi888, mydatabase, nft_nftid, nft_nftid_bak, nft_recent, ollama, ookvipacc, ookvipnft, pc528, taobao_db, tempmail, wemix_news
[17:47:04] 
>> 正在处理数据库: `10010`
[17:47:04]    - 发现 3 个表...
[17:47:04]      - 正在迁移表 `processed_numbers` (42871 行)
[17:47:06]        已迁移 10000/42871 行...
[17:47:07]        已迁移 20000/42871 行...
[17:47:08]        已迁移 30000/42871 行...
[17:47:08]        已迁移 40000/42871 行...
[17:47:09]      - 表 `processed_numbers` 迁移完成 (42871 行)
[17:47:09]      - 正在迁移表 `special_numbers` (41 行)
[17:47:09]      - 表 `special_numbers` 迁移完成 (41 行)
[17:47:09]      - 正在迁移表 `token_cache` (1 行)
[17:47:09]      - 表 `token_cache` 迁移完成 (1 行)
[17:47:09]    - 所有表迁移完成。
[17:47:09] 
>> 正在处理数据库: `bing_db`
[17:47:09]    - 该数据库为空，跳过。
[17:47:09] 
>> 正在处理数据库: `censys`
[17:47:09]    - 发现 1 个表...
[17:47:09]      - 正在迁移表 `data` (1 行)
[17:47:09]      - 表 `data` 迁移完成 (1 行)
[17:47:09]    - 所有表迁移完成。
[17:47:09] 
>> 正在处理数据库: `dingding_MSG`
[17:47:09]    - 发现 1 个表...
[17:47:09]      - 正在迁移表 `notification_records` (12 行)
[17:47:09]      - 表 `notification_records` 迁移完成 (12 行)
[17:47:09]    - 所有表迁移完成。
[17:47:09] 
>> 正在处理数据库: `gi888`
[17:47:09]    - 发现 2 个表...
[17:47:09]      - 正在迁移表 `data` (1755 行)
[17:47:10]      - 表 `data` 迁移完成 (1755 行)
[17:47:10]      - 正在迁移表 `phone_accounts` (1755 行)
[17:47:10]      - 表 `phone_accounts` 迁移完成 (1755 行)
[17:47:10]    - 所有表迁移完成。
[17:47:10] 
>> 正在处理数据库: `mydatabase`
[17:47:10]    - 发现 3 个表...
[17:47:10]      - 正在迁移表 `models` (6477 行)
[17:47:11]      - 表 `models` 迁移完成 (6477 行)
[17:47:11]      - 正在迁移表 `models_copy1` (1670 行)
[17:47:12]      - 表 `models_copy1` 迁移完成 (1670 行)
[17:47:12]      - 正在迁移表 `models_old` (6410 行)
[17:47:13]      - 表 `models_old` 迁移完成 (6410 行)
[17:47:13]    - 所有表迁移完成。
[17:47:13] 
>> 正在处理数据库: `nft_nftid`
[17:47:13]    - 发现 1 个表...
[17:47:13]      - 正在迁移表 `data` (98423 行)
[17:47:14]        已迁移 10000/98423 行...
[17:47:15]        已迁移 20000/98423 行...
[17:47:16]        已迁移 30000/98423 行...
[17:47:17]        已迁移 40000/98423 行...
[17:47:18]        已迁移 50000/98423 行...
[17:47:19]        已迁移 60000/98423 行...
[17:47:19]        已迁移 70000/98423 行...
[17:47:20]        已迁移 80000/98423 行...
[17:47:21]        已迁移 90000/98423 行...
[17:47:22]      - 表 `data` 迁移完成 (98423 行)
[17:47:22]    - 所有表迁移完成。
[17:47:22]    - 发现 1 个事件...
[17:47:22]    - 所有事件迁移完成。
[17:47:22] 
>> 正在处理数据库: `nft_nftid_bak`
[17:47:22]    - 发现 1 个表...
[17:47:23]      - 正在迁移表 `data` (98423 行)
[17:47:24]        已迁移 10000/98423 行...
[17:47:25]        已迁移 20000/98423 行...
[17:47:26]        已迁移 30000/98423 行...
[17:47:27]        已迁移 40000/98423 行...
[17:47:28]        已迁移 50000/98423 行...
[17:47:28]        已迁移 60000/98423 行...
[17:47:29]        已迁移 70000/98423 行...
[17:47:30]        已迁移 80000/98423 行...
[17:47:31]        已迁移 90000/98423 行...
[17:47:32]      - 表 `data` 迁移完成 (98423 行)
[17:47:32]    - 所有表迁移完成。
[17:47:32] 
>> 正在处理数据库: `nft_recent`
[17:47:32]    - 发现 1 个表...
[17:47:32]      - 正在迁移表 `data` (155473 行)
[17:47:34]        已迁移 10000/155473 行...
[17:47:35]        已迁移 20000/155473 行...
[17:47:36]        已迁移 30000/155473 行...
[17:47:37]        已迁移 40000/155473 行...
[17:47:38]        已迁移 50000/155473 行...
[17:47:39]        已迁移 60000/155473 行...
[17:47:40]        已迁移 70000/155473 行...
[17:47:41]        已迁移 80000/155473 行...
[17:47:42]        已迁移 90000/155473 行...
[17:47:43]        已迁移 100000/155473 行...
[17:47:44]        已迁移 110000/155473 行...
[17:47:45]        已迁移 120000/155473 行...
[17:47:46]        已迁移 130000/155473 行...
[17:47:47]        已迁移 140000/155473 行...
[17:47:48]        已迁移 150000/155473 行...
[17:47:48]      - 表 `data` 迁移完成 (155473 行)
[17:47:48]    - 所有表迁移完成。
[17:47:48] 
>> 正在处理数据库: `ollama`
[17:47:48]    - 该数据库为空，跳过。
[17:47:48] 
>> 正在处理数据库: `ookvipacc`
[17:47:48]    - 发现 1 个表...
[17:47:48]      - 正在迁移表 `Account` (3 行)
[17:47:48]      - 表 `Account` 迁移完成 (3 行)
[17:47:48]    - 所有表迁移完成。
[17:47:48] 
>> 正在处理数据库: `ookvipnft`
[17:47:48]    - 发现 1 个表...
[17:47:49]      - 正在迁移表 `data` (1855 行)
[17:47:49]      - 表 `data` 迁移完成 (1855 行)
[17:47:49]    - 所有表迁移完成。
[17:47:49] 
>> 正在处理数据库: `pc528`
[17:47:49]    - 发现 3 个表...
[17:47:49]      - 正在迁移表 `access_logs` (1091 行)
[17:47:49]      - 表 `access_logs` 迁移完成 (1091 行)
[17:47:50]      - 正在迁移表 `articles` (47 行)
[17:47:50]      - 表 `articles` 迁移完成 (47 行)
[17:47:50]      - 正在迁移表 `auth_keys` (76 行)
[17:47:50]      - 表 `auth_keys` 迁移完成 (76 行)
[17:47:50]    - 所有表迁移完成。
[17:47:50] 
>> 正在处理数据库: `taobao_db`
[17:47:50]    - 发现 3 个表...
[17:47:50]      - 正在迁移表 `cookies` (1 行)
[17:47:50]      - 表 `cookies` 迁移完成 (1 行)
[17:47:50]      - 表 `monitor_tasks` 为空，跳过数据迁移
[17:47:51]      - 正在迁移表 `taobao_data` (536 行)
[17:47:51]      - 表 `taobao_data` 迁移完成 (536 行)
[17:47:51]    - 所有表迁移完成。
[17:47:51] 
>> 正在处理数据库: `tempmail`
[17:47:51]    - 发现 2 个表...
[17:47:51]      - 正在迁移表 `email_history` (46 行)
[17:47:51]      - 表 `email_history` 迁移完成 (46 行)
[17:47:51]      - 正在迁移表 `email_providers` (35 行)
[17:47:51]      - 表 `email_providers` 迁移完成 (35 行)
[17:47:51]    - 所有表迁移完成。
[17:47:51] 
>> 正在处理数据库: `wemix_news`
[17:47:51]    - 发现 1 个表...
[17:47:51]      - 正在迁移表 `wemix_news` (2535 行)
[17:47:52]      - 表 `wemix_news` 迁移完成 (2535 行)
[17:47:52]    - 所有表迁移完成。
[17:47:52] 
--- 所有数据和结构迁移成功！ ---
[17:47:52] 所有数据库连接已关闭。
[17:47:52] 
🎉🎉🎉 所有任务成功完成！ 🎉🎉🎉
[17:47:59] 
==============================================
[17:47:59]           开始执行迁移任务...           
[17:47:59] ==============================================
[17:47:59] 调试: 迁移任务类型 - users
[17:47:59] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:47:59] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:47:59] --- 开始迁移用户和权限 ---
[17:47:59] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb3
[17:47:59] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[17:47:59] 连接源数据库...
[17:47:59] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:47:59] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:47:59] 调试: 错误详情 - errno: -1, sqlstate: None
[17:47:59] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:47:59] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:48:00] 
==============================================
[17:48:00]           开始执行迁移任务...           
[17:48:00] ==============================================
[17:48:00] 调试: 迁移任务类型 - users
[17:48:00] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:48:00] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:48:00] --- 开始迁移用户和权限 ---
[17:48:00] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb3
[17:48:00] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[17:48:00] 连接源数据库...
[17:48:00] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:48:00] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:48:00] 调试: 错误详情 - errno: -1, sqlstate: None
[17:48:00] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:48:00] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:48:01] 
==============================================
[17:48:01]           开始执行迁移任务...           
[17:48:01] ==============================================
[17:48:01] 调试: 迁移任务类型 - users
[17:48:01] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:48:01] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:48:01] --- 开始迁移用户和权限 ---
[17:48:01] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb3
[17:48:01] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[17:48:01] 连接源数据库...
[17:48:01] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:48:01] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:48:01] 调试: 错误详情 - errno: -1, sqlstate: None
[17:48:01] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:48:01] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:50:43] 已从 db_config.json 加载配置。
[17:50:45] 正在测试 source 数据库连接...
[17:50:45] 正在测试连接到 *************...
[17:50:45] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:50:45] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:50:45] 连接成功！
[17:50:55] 正在测试 dest 数据库连接...
[17:50:55] 正在测试连接到 *************...
[17:50:55] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[17:50:55] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:50:55] 连接成功！
[17:50:56] 
==============================================
[17:50:56]           开始执行迁移任务...           
[17:50:56] ==============================================
[17:50:56] 调试: 迁移任务类型 - full
[17:50:56] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:50:56] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:50:56] 步骤 1/2: 迁移数据和结构
[17:50:56] --- 开始迁移数据和结构 ---
[17:50:56] 连接源数据库...
[17:50:56] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:50:56] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:50:56] 调试: 错误详情 - errno: -1, sqlstate: None
[17:50:56] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:50:56] 所有数据库连接已关闭。
[17:50:56] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:51:00] 
==============================================
[17:51:00]           开始执行迁移任务...           
[17:51:00] ==============================================
[17:51:00] 调试: 迁移任务类型 - full
[17:51:00] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:51:00] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:51:00] 步骤 1/2: 迁移数据和结构
[17:51:00] --- 开始迁移数据和结构 ---
[17:51:00] 连接源数据库...
[17:51:00] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:51:00] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:51:00] 调试: 错误详情 - errno: -1, sqlstate: None
[17:51:00] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:51:00] 所有数据库连接已关闭。
[17:51:00] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:51:02] 
==============================================
[17:51:02]           开始执行迁移任务...           
[17:51:02] ==============================================
[17:51:02] 调试: 迁移任务类型 - data
[17:51:02] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:51:02] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:51:02] --- 开始迁移数据和结构 ---
[17:51:02] 连接源数据库...
[17:51:02] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:51:02] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:51:02] 调试: 错误详情 - errno: -1, sqlstate: None
[17:51:02] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:51:02] 所有数据库连接已关闭。
[17:51:02] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:51:05] 已从 db_config.json 加载配置。
[17:51:08] 
==============================================
[17:51:08]           开始执行迁移任务...           
[17:51:08] ==============================================
[17:51:08] 调试: 迁移任务类型 - full
[17:51:08] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb3
[17:51:08] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:51:08] 步骤 1/2: 迁移数据和结构
[17:51:08] --- 开始迁移数据和结构 ---
[17:51:08] 连接源数据库...
[17:51:08] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:51:08] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:51:08] 连接目标数据库...
[17:51:08] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[17:51:08] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:51:08] 发现 16 个用户数据库待迁移: 10010, bing_db, censys, dingding_MSG, gi888, mydatabase, nft_nftid, nft_nftid_bak, nft_recent, ollama, ookvipacc, ookvipnft, pc528, taobao_db, tempmail, wemix_news
[17:51:08] 
>> 正在处理数据库: `10010`
[17:51:08]    - 发现 3 个表...
[17:51:08]      - 正在迁移表 `processed_numbers` (42871 行)
[17:51:10]        已迁移 10000/42871 行...
[17:51:11]        已迁移 20000/42871 行...
[17:51:11]        已迁移 30000/42871 行...
[17:51:12]        已迁移 40000/42871 行...
[17:51:12]      - 表 `processed_numbers` 迁移完成 (42871 行)
[17:51:12]      - 正在迁移表 `special_numbers` (41 行)
[17:51:12]      - 表 `special_numbers` 迁移完成 (41 行)
[17:51:13]      - 正在迁移表 `token_cache` (1 行)
[17:51:13]      - 表 `token_cache` 迁移完成 (1 行)
[17:51:13]    - 所有表迁移完成。
[17:51:13] 
>> 正在处理数据库: `bing_db`
[17:51:13]    - 该数据库为空，跳过。
[17:51:13] 
>> 正在处理数据库: `censys`
[17:51:13]    - 发现 1 个表...
[17:51:13]      - 正在迁移表 `data` (1 行)
[17:51:13]      - 表 `data` 迁移完成 (1 行)
[17:51:13]    - 所有表迁移完成。
[17:51:13] 
>> 正在处理数据库: `dingding_MSG`
[17:51:13]    - 发现 1 个表...
[17:51:13]      - 正在迁移表 `notification_records` (12 行)
[17:51:13]      - 表 `notification_records` 迁移完成 (12 行)
[17:51:13]    - 所有表迁移完成。
[17:51:13] 
>> 正在处理数据库: `gi888`
[17:51:13]    - 发现 2 个表...
[17:51:13]      - 正在迁移表 `data` (1755 行)
[17:51:13]      - 表 `data` 迁移完成 (1755 行)
[17:51:13]      - 正在迁移表 `phone_accounts` (1755 行)
[17:51:14]      - 表 `phone_accounts` 迁移完成 (1755 行)
[17:51:14]    - 所有表迁移完成。
[17:51:14] 
>> 正在处理数据库: `mydatabase`
[17:51:14]    - 发现 3 个表...
[17:51:14]      - 正在迁移表 `models` (6477 行)
[17:51:15]      - 表 `models` 迁移完成 (6477 行)
[17:51:15]      - 正在迁移表 `models_copy1` (1670 行)
[17:51:15]      - 表 `models_copy1` 迁移完成 (1670 行)
[17:51:15]      - 正在迁移表 `models_old` (6410 行)
[17:51:17]      - 表 `models_old` 迁移完成 (6410 行)
[17:51:17]    - 所有表迁移完成。
[17:51:17] 
>> 正在处理数据库: `nft_nftid`
[17:51:17]    - 发现 1 个表...
[17:51:17]      - 正在迁移表 `data` (98423 行)
[17:51:18]        已迁移 10000/98423 行...
[17:51:19]        已迁移 20000/98423 行...
[17:51:20]        已迁移 30000/98423 行...
[17:51:21]        已迁移 40000/98423 行...
[17:51:22]        已迁移 50000/98423 行...
[17:51:23]        已迁移 60000/98423 行...
[17:51:24]        已迁移 70000/98423 行...
[17:51:25]        已迁移 80000/98423 行...
[17:51:25]        已迁移 90000/98423 行...
[17:51:26]      - 表 `data` 迁移完成 (98423 行)
[17:51:26]    - 所有表迁移完成。
[17:51:26]    - 发现 1 个事件...
[17:51:26]    - 所有事件迁移完成。
[17:51:26] 
>> 正在处理数据库: `nft_nftid_bak`
[17:51:26]    - 发现 1 个表...
[17:51:27]      - 正在迁移表 `data` (98423 行)
[17:51:28]        已迁移 10000/98423 行...
[17:51:29]        已迁移 20000/98423 行...
[17:51:30]        已迁移 30000/98423 行...
[17:51:31]        已迁移 40000/98423 行...
[17:51:31]        已迁移 50000/98423 行...
[17:51:32]        已迁移 60000/98423 行...
[17:51:33]        已迁移 70000/98423 行...
[17:51:34]        已迁移 80000/98423 行...
[17:51:35]        已迁移 90000/98423 行...
[17:51:36]      - 表 `data` 迁移完成 (98423 行)
[17:51:36]    - 所有表迁移完成。
[17:51:36] 
>> 正在处理数据库: `nft_recent`
[17:51:36]    - 发现 1 个表...
[17:51:36]      - 正在迁移表 `data` (155473 行)
[17:51:38]        已迁移 10000/155473 行...
[17:51:39]        已迁移 20000/155473 行...
[17:51:40]        已迁移 30000/155473 行...
[17:51:41]        已迁移 40000/155473 行...
[17:51:42]        已迁移 50000/155473 行...
[17:51:42]        已迁移 60000/155473 行...
[17:51:44]        已迁移 70000/155473 行...
[17:51:45]        已迁移 80000/155473 行...
[17:51:46]        已迁移 90000/155473 行...
[17:51:47]        已迁移 100000/155473 行...
[17:51:48]        已迁移 110000/155473 行...
[17:51:49]        已迁移 120000/155473 行...
[17:51:50]        已迁移 130000/155473 行...
[17:51:51]        已迁移 140000/155473 行...
[17:51:52]        已迁移 150000/155473 行...
[17:51:53]      - 表 `data` 迁移完成 (155473 行)
[17:51:53]    - 所有表迁移完成。
[17:51:53] 
>> 正在处理数据库: `ollama`
[17:51:53]    - 该数据库为空，跳过。
[17:51:53] 
>> 正在处理数据库: `ookvipacc`
[17:51:53]    - 发现 1 个表...
[17:51:53]      - 正在迁移表 `Account` (3 行)
[17:51:53]      - 表 `Account` 迁移完成 (3 行)
[17:51:53]    - 所有表迁移完成。
[17:51:53] 
>> 正在处理数据库: `ookvipnft`
[17:51:53]    - 发现 1 个表...
[17:51:53]      - 正在迁移表 `data` (1855 行)
[17:51:54]      - 表 `data` 迁移完成 (1855 行)
[17:51:54]    - 所有表迁移完成。
[17:51:54] 
>> 正在处理数据库: `pc528`
[17:51:54]    - 发现 3 个表...
[17:51:54]      - 正在迁移表 `access_logs` (1091 行)
[17:51:54]      - 表 `access_logs` 迁移完成 (1091 行)
[17:51:55]      - 正在迁移表 `articles` (47 行)
[17:51:55]      - 表 `articles` 迁移完成 (47 行)
[17:51:55]      - 正在迁移表 `auth_keys` (76 行)
[17:51:55]      - 表 `auth_keys` 迁移完成 (76 行)
[17:51:55]    - 所有表迁移完成。
[17:51:55] 
>> 正在处理数据库: `taobao_db`
[17:51:55]    - 发现 3 个表...
[17:51:55]      - 正在迁移表 `cookies` (1 行)
[17:51:55]      - 表 `cookies` 迁移完成 (1 行)
[17:51:55]      - 表 `monitor_tasks` 为空，跳过数据迁移
[17:51:55]      - 正在迁移表 `taobao_data` (536 行)
[17:51:55]      - 表 `taobao_data` 迁移完成 (536 行)
[17:51:55]    - 所有表迁移完成。
[17:51:55] 
>> 正在处理数据库: `tempmail`
[17:51:55]    - 发现 2 个表...
[17:51:56]      - 正在迁移表 `email_history` (46 行)
[17:51:56]      - 表 `email_history` 迁移完成 (46 行)
[17:51:56]      - 正在迁移表 `email_providers` (35 行)
[17:51:56]      - 表 `email_providers` 迁移完成 (35 行)
[17:51:56]    - 所有表迁移完成。
[17:51:56] 
>> 正在处理数据库: `wemix_news`
[17:51:56]    - 发现 1 个表...
[17:51:56]      - 正在迁移表 `wemix_news` (2535 行)
[17:51:57]      - 表 `wemix_news` 迁移完成 (2535 行)
[17:51:57]    - 所有表迁移完成。
[17:51:57] 
--- 所有数据和结构迁移成功！ ---
[17:51:57] 所有数据库连接已关闭。
[17:51:57] 等待连接资源释放 (2秒)...
[17:51:59] 
步骤 2/2: 迁移用户和权限
[17:51:59] --- 开始迁移用户和权限 ---
[17:51:59] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb3
[17:51:59] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[17:51:59] 连接源数据库...
[17:51:59] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:51:59] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:51:59] 调试: 错误详情 - errno: -1, sqlstate: None
[17:51:59] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:51:59] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[17:52:09] 正在测试 source 数据库连接...
[17:52:09] 正在测试连接到 *************...
[17:52:09] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:52:09] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:52:09] 调试: 错误详情 - errno: -1, sqlstate: None
[17:52:09] 连接失败: Character set 'utf8' unsupported
[17:52:11] 正在测试 dest 数据库连接...
[17:52:11] 正在测试连接到 *************...
[17:52:11] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[17:52:11] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:52:11] 连接成功！
[17:52:12] 正在测试 source 数据库连接...
[17:52:12] 正在测试连接到 *************...
[17:52:12] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:52:12] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:52:12] 调试: 错误详情 - errno: -1, sqlstate: None
[17:52:12] 连接失败: Character set 'utf8' unsupported
[17:52:13] 正在测试 source 数据库连接...
[17:52:13] 正在测试连接到 *************...
[17:52:13] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb3
[17:52:13] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:52:13] 调试: 错误详情 - errno: -1, sqlstate: None
[17:52:13] 连接失败: Character set 'utf8' unsupported
[18:27:13] 已从 db_config.json 加载配置。
[18:27:15] 
==============================================
[18:27:15]           开始执行迁移任务...           
[18:27:15] ==============================================
[18:27:15] 调试: 迁移任务类型 - full
[18:27:15] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:27:15] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:27:15] 步骤 1/2: 迁移数据和结构
[18:27:15] --- 开始迁移数据和结构 ---
[18:27:15] 连接源数据库...
[18:27:15] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:27:15] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[18:27:15] 连接目标数据库...
[18:27:15] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:27:15] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[18:27:15] 发现 16 个用户数据库待迁移: 10010, bing_db, censys, dingding_MSG, gi888, mydatabase, nft_nftid, nft_nftid_bak, nft_recent, ollama, ookvipacc, ookvipnft, pc528, taobao_db, tempmail, wemix_news
[18:27:15] 
>> 正在处理数据库: `10010`
[18:27:15]    - 发现 3 个表...
[18:27:15]      - 正在迁移表 `processed_numbers` (42872 行)
[18:27:16]        已迁移 10000/42872 行...
[18:27:17]        已迁移 20000/42872 行...
[18:27:18]        已迁移 30000/42872 行...
[18:27:18]        已迁移 40000/42872 行...
[18:27:18]      - 表 `processed_numbers` 迁移完成 (42872 行)
[18:27:19]      - 正在迁移表 `special_numbers` (41 行)
[18:27:19]      - 表 `special_numbers` 迁移完成 (41 行)
[18:27:19]      - 正在迁移表 `token_cache` (1 行)
[18:27:19]      - 表 `token_cache` 迁移完成 (1 行)
[18:27:19]    - 所有表迁移完成。
[18:27:19] 
>> 正在处理数据库: `bing_db`
[18:27:19]    - 该数据库为空，跳过。
[18:27:19] 
>> 正在处理数据库: `censys`
[18:27:19]    - 发现 1 个表...
[18:27:19]      - 正在迁移表 `data` (1 行)
[18:27:19]      - 表 `data` 迁移完成 (1 行)
[18:27:19]    - 所有表迁移完成。
[18:27:19] 
>> 正在处理数据库: `dingding_MSG`
[18:27:19]    - 发现 1 个表...
[18:27:19]      - 正在迁移表 `notification_records` (12 行)
[18:27:19]      - 表 `notification_records` 迁移完成 (12 行)
[18:27:19]    - 所有表迁移完成。
[18:27:19] 
>> 正在处理数据库: `gi888`
[18:27:19]    - 发现 2 个表...
[18:27:19]      - 正在迁移表 `data` (1755 行)
[18:27:19]      - 表 `data` 迁移完成 (1755 行)
[18:27:19]      - 正在迁移表 `phone_accounts` (1755 行)
[18:27:20]      - 表 `phone_accounts` 迁移完成 (1755 行)
[18:27:20]    - 所有表迁移完成。
[18:27:20] 
>> 正在处理数据库: `mydatabase`
[18:27:20]    - 发现 3 个表...
[18:27:20]      - 正在迁移表 `models` (6477 行)
[18:27:21]      - 表 `models` 迁移完成 (6477 行)
[18:27:21]      - 正在迁移表 `models_copy1` (1670 行)
[18:27:21]      - 表 `models_copy1` 迁移完成 (1670 行)
[18:27:21]      - 正在迁移表 `models_old` (6410 行)
[18:27:22]      - 表 `models_old` 迁移完成 (6410 行)
[18:27:22]    - 所有表迁移完成。
[18:27:22] 
>> 正在处理数据库: `nft_nftid`
[18:27:22]    - 发现 1 个表...
[18:27:22]      - 正在迁移表 `data` (98423 行)
[18:27:24]        已迁移 10000/98423 行...
[18:27:24]        已迁移 20000/98423 行...
[18:27:25]        已迁移 30000/98423 行...
[18:27:26]        已迁移 40000/98423 行...
[18:27:27]        已迁移 50000/98423 行...
[18:27:28]        已迁移 60000/98423 行...
[18:27:29]        已迁移 70000/98423 行...
[18:27:29]        已迁移 80000/98423 行...
[18:27:30]        已迁移 90000/98423 行...
[18:27:31]      - 表 `data` 迁移完成 (98423 行)
[18:27:31]    - 所有表迁移完成。
[18:27:31]    - 发现 1 个事件...
[18:27:31]    - 所有事件迁移完成。
[18:27:31] 
>> 正在处理数据库: `nft_nftid_bak`
[18:27:31]    - 发现 1 个表...
[18:27:31]      - 正在迁移表 `data` (98423 行)
[18:27:33]        已迁移 10000/98423 行...
[18:27:34]        已迁移 20000/98423 行...
[18:27:35]        已迁移 30000/98423 行...
[18:27:36]        已迁移 40000/98423 行...
[18:27:37]        已迁移 50000/98423 行...
[18:27:38]        已迁移 60000/98423 行...
[18:27:38]        已迁移 70000/98423 行...
[18:27:39]        已迁移 80000/98423 行...
[18:27:40]        已迁移 90000/98423 行...
[18:27:41]      - 表 `data` 迁移完成 (98423 行)
[18:27:41]    - 所有表迁移完成。
[18:27:41] 
>> 正在处理数据库: `nft_recent`
[18:27:41]    - 发现 1 个表...
[18:27:41]      - 正在迁移表 `data` (155479 行)
[18:27:42]        已迁移 10000/155479 行...
[18:27:43]        已迁移 20000/155479 行...
[18:27:44]        已迁移 30000/155479 行...
[18:27:45]        已迁移 40000/155479 行...
[18:27:46]        已迁移 50000/155479 行...
[18:27:47]        已迁移 60000/155479 行...
[18:27:48]        已迁移 70000/155479 行...
[18:27:49]        已迁移 80000/155479 行...
[18:27:50]        已迁移 90000/155479 行...
[18:27:51]        已迁移 100000/155479 行...
[18:27:52]        已迁移 110000/155479 行...
[18:27:54]        已迁移 120000/155479 行...
[18:27:55]        已迁移 130000/155479 行...
[18:27:56]        已迁移 140000/155479 行...
[18:27:57]        已迁移 150000/155479 行...
[18:27:58]      - 表 `data` 迁移完成 (155479 行)
[18:27:58]    - 所有表迁移完成。
[18:27:58] 
>> 正在处理数据库: `ollama`
[18:27:58]    - 该数据库为空，跳过。
[18:27:58] 
>> 正在处理数据库: `ookvipacc`
[18:27:58]    - 发现 1 个表...
[18:27:58]      - 正在迁移表 `Account` (3 行)
[18:27:58]      - 表 `Account` 迁移完成 (3 行)
[18:27:58]    - 所有表迁移完成。
[18:27:58] 
>> 正在处理数据库: `ookvipnft`
[18:27:58]    - 发现 1 个表...
[18:27:58]      - 正在迁移表 `data` (1855 行)
[18:27:59]      - 表 `data` 迁移完成 (1855 行)
[18:27:59]    - 所有表迁移完成。
[18:27:59] 
>> 正在处理数据库: `pc528`
[18:27:59]    - 发现 3 个表...
[18:27:59]      - 正在迁移表 `access_logs` (1091 行)
[18:28:00]      - 表 `access_logs` 迁移完成 (1091 行)
[18:28:00]      - 正在迁移表 `articles` (47 行)
[18:28:00]      - 表 `articles` 迁移完成 (47 行)
[18:28:00]      - 正在迁移表 `auth_keys` (76 行)
[18:28:00]      - 表 `auth_keys` 迁移完成 (76 行)
[18:28:00]    - 所有表迁移完成。
[18:28:00] 
>> 正在处理数据库: `taobao_db`
[18:28:00]    - 发现 3 个表...
[18:28:00]      - 正在迁移表 `cookies` (1 行)
[18:28:00]      - 表 `cookies` 迁移完成 (1 行)
[18:28:00]      - 表 `monitor_tasks` 为空，跳过数据迁移
[18:28:00]      - 正在迁移表 `taobao_data` (537 行)
[18:28:01]      - 表 `taobao_data` 迁移完成 (537 行)
[18:28:01]    - 所有表迁移完成。
[18:28:01] 
>> 正在处理数据库: `tempmail`
[18:28:01]    - 发现 2 个表...
[18:28:01]      - 正在迁移表 `email_history` (46 行)
[18:28:01]      - 表 `email_history` 迁移完成 (46 行)
[18:28:01]      - 正在迁移表 `email_providers` (35 行)
[18:28:01]      - 表 `email_providers` 迁移完成 (35 行)
[18:28:01]    - 所有表迁移完成。
[18:28:01] 
>> 正在处理数据库: `wemix_news`
[18:28:01]    - 发现 1 个表...
[18:28:01]      - 正在迁移表 `wemix_news` (2535 行)
[18:28:02]      - 表 `wemix_news` 迁移完成 (2535 行)
[18:28:02]    - 所有表迁移完成。
[18:28:02] 
--- 所有数据和结构迁移成功！ ---
[18:28:02] 调试: 游标已关闭
[18:28:02] 调试: 数据库连接已关闭
[18:28:02] 调试: 游标已关闭
[18:28:02] 调试: 数据库连接已关闭
[18:28:02] 所有数据库连接已关闭。
[18:28:02] 等待连接资源释放 (5秒)...
[18:28:07] 
步骤 2/2: 迁移用户和权限
[18:28:07] --- 开始迁移用户和权限 ---
[18:28:07] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb4
[18:28:07] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[18:28:07] 连接源数据库...
[18:28:07] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:28:07] 调试: 使用字符集 utf8mb4 连接失败: Character set 'utf8' unsupported
[18:28:07] 调试: 使用字符集 utf8mb4 连接失败: Character set 'utf8' unsupported
[18:28:07] 调试: 使用字符集 utf8mb3 连接失败: Character set 'utf8' unsupported
[18:28:07] 调试: 使用字符集 latin1 连接失败: Character set 'utf8' unsupported
[18:28:07] ✗ 数据库连接失败: 所有字符集连接尝试都失败了
[18:28:07] 调试: 错误详情 - errno: -1, sqlstate: None
[18:28:07] 
[错误] 数据库操作失败: 所有字符集连接尝试都失败了
[18:28:08] 所有数据库连接已关闭。
[18:28:08] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[18:50:39] 
==============================================
[18:50:39]           开始执行迁移任务...           
[18:50:39] ==============================================
[18:50:39] 调试: 迁移任务类型 - users
[18:50:39] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:50:39] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:50:39] --- 开始迁移用户和权限 ---
[18:50:39] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb4
[18:50:39] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[18:50:39] 连接源数据库...
[18:50:39] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:50:39] 调试: 使用字符集 utf8mb4 连接失败: Character set 'utf8' unsupported
[18:50:39] 调试: 使用字符集 utf8mb4 连接失败: Character set 'utf8' unsupported
[18:50:39] 调试: 使用字符集 utf8mb3 连接失败: Character set 'utf8' unsupported
[18:50:39] 调试: 使用字符集 latin1 连接失败: Character set 'utf8' unsupported
[18:50:39] ✗ 数据库连接失败: 所有字符集连接尝试都失败了
[18:50:39] 调试: 错误详情 - errno: -1, sqlstate: None
[18:50:39] 
[错误] 数据库操作失败: 所有字符集连接尝试都失败了
[18:50:40] 所有数据库连接已关闭。
[18:50:40] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[18:50:43] 已从 db_config.json 加载配置。
[18:50:47] 
==============================================
[18:50:47]           开始执行迁移任务...           
[18:50:47] ==============================================
[18:50:47] 调试: 迁移任务类型 - users
[18:50:47] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:50:47] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:50:47] --- 开始迁移用户和权限 ---
[18:50:47] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb4
[18:50:47] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[18:50:47] 连接源数据库...
[18:50:47] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:50:47] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[18:50:47] 发现 2 个非系统用户待迁移。
[18:50:47]   -> 正在处理用户: 'gi'@'%'
[18:50:47]   -> 正在处理用户: 'taobao'@'%'
[18:50:47] 
模式: 直接在目标数据库执行命令...
[18:50:47] 连接目标数据库...
[18:50:47] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:50:47] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[18:50:47] 所有用户和权限已直接在目标数据库上创建/更新。
[18:50:47] 
--- 用户和权限迁移任务完成！ ---
[18:50:47] 调试: 游标已关闭
[18:50:47] 调试: 数据库连接已关闭
[18:50:47] 调试: 游标已关闭
[18:50:47] 调试: 数据库连接已关闭
[18:50:48] 所有数据库连接已关闭。
[18:50:48] 
🎉🎉🎉 所有任务成功完成！ 🎉🎉🎉
[18:51:06] 已从 db_config.json 加载配置。
[18:51:07] 正在测试 source 数据库连接...
[18:51:07] 正在测试连接到 *************...
[18:51:07] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:51:07] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[18:51:07] 连接成功！
[18:51:09] 正在测试 dest 数据库连接...
[18:51:09] 正在测试连接到 *************...
[18:51:09] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:51:09] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[18:51:09] 连接成功！
[18:51:13] 
==============================================
[18:51:13]           开始执行迁移任务...           
[18:51:13] ==============================================
[18:51:13] 调试: 迁移任务类型 - users
[18:51:13] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:51:13] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:51:13] --- 开始迁移用户和权限 ---
[18:51:13] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb4
[18:51:13] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[18:51:13] 连接源数据库...
[18:51:13] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:51:13] 调试: 使用字符集 utf8mb4 连接失败: Character set 'utf8' unsupported
[18:51:13] 调试: 使用字符集 utf8mb4 连接失败: Character set 'utf8' unsupported
[18:51:13] 调试: 使用字符集 utf8mb3 连接失败: Character set 'utf8' unsupported
[18:51:13] 调试: 使用字符集 latin1 连接失败: Character set 'utf8' unsupported
[18:51:13] ✗ 数据库连接失败: 所有字符集连接尝试都失败了
[18:51:13] 调试: 错误详情 - errno: -1, sqlstate: None
[18:51:13] 
[错误] 数据库操作失败: 所有字符集连接尝试都失败了
[18:51:14] 所有数据库连接已关闭。
[18:51:14] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[18:51:21] 
==============================================
[18:51:21]           开始执行迁移任务...           
[18:51:21] ==============================================
[18:51:21] 调试: 迁移任务类型 - users
[18:51:21] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:51:21] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[18:51:21] --- 开始迁移用户和权限 ---
[18:51:21] 调试: 用户迁移阶段 - 源数据库字符集: utf8mb4
[18:51:21] 调试: 用户迁移阶段 - 目标数据库字符集: utf8mb4
[18:51:21] 连接源数据库...
[18:51:21] 调试: 连接参数 - host=*************, port=3306, user=root, charset=utf8mb4
[18:51:21] 调试: 使用字符集 utf8mb4 连接失败: Character set 'utf8' unsupported
[18:51:21] 调试: 使用字符集 utf8mb4 连接失败: Character set 'utf8' unsupported
[18:51:21] 调试: 使用字符集 utf8mb3 连接失败: Character set 'utf8' unsupported
[18:51:21] 调试: 使用字符集 latin1 连接失败: Character set 'utf8' unsupported
[18:51:21] ✗ 数据库连接失败: 所有字符集连接尝试都失败了
[18:51:21] 调试: 错误详情 - errno: -1, sqlstate: None
[18:51:21] 
[错误] 数据库操作失败: 所有字符集连接尝试都失败了
[18:51:22] 所有数据库连接已关闭。
[18:51:22] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
