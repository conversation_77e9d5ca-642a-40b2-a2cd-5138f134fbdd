"""
数据库迁移工具 - 图形用户界面 (GUI)
-------------------------------------
运行此脚本以启动应用程序。
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import queue
import json
import os
import datetime

# 导入核心迁移逻辑
import migration_core

# --- 配置文件路径 ---
CONFIG_FILE = 'db_config.json'

class MigrationApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("数据库迁移工具 (GUI)")

        # 优化：设置窗口大小并直接居中，避免闪烁
        window_width = 960
        window_height = 720
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        center_x = int(screen_width / 2 - window_width / 2)
        center_y = int(screen_height / 2 - window_height / 2)
        self.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')
        self.minsize(800, 650) # 设置一个合理的最小尺寸

        self.log_queue = queue.Queue()
        self.create_widgets()
        self.load_config()

        # 绑定窗口关闭事件
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.is_running = False
        
        # 启动日志消息轮询
        self.after(100, self.process_log_queue)

    def create_widgets(self):
        # --- 主框架 ---
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # --- 配置框架 (上半部分) ---
        config_frame = ttk.Labelframe(main_frame, text="数据库配置", padding="10")
        config_frame.pack(fill=tk.X, expand=True, side=tk.TOP)

        # 优化布局：使用更均匀的列权重分配
        config_frame.grid_columnconfigure(0, weight=0)  # 标签列，固定宽度
        config_frame.grid_columnconfigure(1, weight=1)  # 源数据库输入列，可扩展
        config_frame.grid_columnconfigure(2, weight=0)  # 分隔符列，固定宽度
        config_frame.grid_columnconfigure(3, weight=0)  # 标签列，固定宽度
        config_frame.grid_columnconfigure(4, weight=1)  # 目标数据库输入列，可扩展

        self.entries = {}
        # 源数据库
        ttk.Label(config_frame, text="源数据库 (MariaDB / MySQL)", font=('', 10, 'bold')).grid(row=0, column=0, columnspan=2, pady=(5,10), sticky='w')
        self.add_config_entry(config_frame, 'source', '主机/IP:', 1)
        self.add_config_entry(config_frame, 'source', '端口:', 2)
        self.add_config_entry(config_frame, 'source', '用户名:', 3)
        self.add_config_entry(config_frame, 'source', '密码:', 4, show='*')

        # 分隔符
        ttk.Separator(config_frame, orient='vertical').grid(row=1, column=2, rowspan=4, sticky='ns', padx=15)

        # 目标数据库
        ttk.Label(config_frame, text="目标数据库 (MySQL / MariaDB)", font=('', 10, 'bold')).grid(row=0, column=3, columnspan=2, pady=(5,10), sticky='w')
        self.add_config_entry(config_frame, 'dest', '主机/IP:', 1, col_offset=3)
        self.add_config_entry(config_frame, 'dest', '端口:', 2, col_offset=3)
        self.add_config_entry(config_frame, 'dest', '用户名:', 3, col_offset=3)
        self.add_config_entry(config_frame, 'dest', '密码:', 4, col_offset=3, show='*')

        # --- 迁移选项框架 ---
        options_frame = ttk.Labelframe(main_frame, text="迁移选项", padding="10")
        options_frame.pack(fill=tk.X, expand=True, side=tk.TOP, pady=10)
        
        self.migration_task = tk.StringVar(value="full")
        self.direct_execute_users = tk.BooleanVar(value=False)
        
        ttk.Radiobutton(options_frame, text="完整迁移 (数据 + 用户)", variable=self.migration_task, value="full", command=self.toggle_user_options).pack(anchor=tk.W)
        ttk.Radiobutton(options_frame, text="仅迁移数据和结构", variable=self.migration_task, value="data", command=self.toggle_user_options).pack(anchor=tk.W)
        ttk.Radiobutton(options_frame, text="仅迁移用户和权限", variable=self.migration_task, value="users", command=self.toggle_user_options).pack(anchor=tk.W)

        self.user_mode_check = ttk.Checkbutton(options_frame, text="直接在目标库执行用户迁移 (全自动模式)", variable=self.direct_execute_users)
        self.user_mode_check.pack(anchor=tk.W, padx=20)

        # --- 控制按钮框架 ---
        control_frame = ttk.Frame(main_frame, padding="5")
        control_frame.pack(fill=tk.X, expand=True, side=tk.TOP)

        # 第一行按钮：连接测试
        test_frame = ttk.Frame(control_frame)
        test_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(test_frame, text="测试源数据库连接", command=lambda: self.test_connection('source')).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        ttk.Button(test_frame, text="测试目标数据库连接", command=lambda: self.test_connection('dest')).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))

        # 第二行按钮：主要操作
        main_control_frame = ttk.Frame(control_frame)
        main_control_frame.pack(fill=tk.X)

        self.start_button = ttk.Button(main_control_frame, text="开始迁移", command=self.start_migration)
        self.start_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))

        ttk.Button(main_control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
        ttk.Button(main_control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))

        # --- 日志输出框架 ---
        log_frame = ttk.Labelframe(main_frame, text="实时日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, side=tk.TOP, pady=10)

        self.log_area = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, state='disabled', height=15)
        self.log_area.pack(fill=tk.BOTH, expand=True)
    
    def add_config_entry(self, parent, db_type, label, row, col_offset=0, **kwargs):
        # 优化标签和输入框的布局
        label_widget = ttk.Label(parent, text=label, width=10)
        label_widget.grid(row=row, column=col_offset, sticky=tk.W, padx=(5, 10), pady=3)

        key = f"{db_type}_{label.split(':')[0].lower()}"
        self.entries[key] = ttk.Entry(parent, width=25, **kwargs)
        self.entries[key].grid(row=row, column=col_offset + 1, sticky=tk.EW, padx=(0, 5), pady=3)

    def toggle_user_options(self):
        if self.migration_task.get() in ["full", "users"]:
            self.user_mode_check.config(state=tk.NORMAL)
        else:
            self.user_mode_check.config(state=tk.DISABLED)
            self.direct_execute_users.set(False)

    def log(self, message):
        """记录日志消息，同时显示在UI和保存到文件"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_queue.put(formatted_message)

        # 同时写入日志文件
        try:
            with open('migration.log', 'a', encoding='utf-8') as f:
                f.write(f"{formatted_message}\n")
        except Exception:
            pass  # 忽略日志文件写入错误

    def process_log_queue(self):
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_area.config(state='normal')
                self.log_area.insert(tk.END, message + '\n')
                self.log_area.config(state='disabled')
                self.log_area.see(tk.END)
        except queue.Empty:
            pass
        finally:
            self.after(100, self.process_log_queue)

    def clear_log(self):
        self.log_area.config(state='normal')
        self.log_area.delete(1.0, tk.END)
        self.log_area.config(state='disabled')

    def test_connection(self, db_type):
        """测试数据库连接"""
        if self.is_running:
            self.log("迁移任务正在进行中，无法测试连接。")
            return

        configs = self.get_configs_from_ui()
        config = configs[db_type]

        # 检查必填字段
        if not all([config['host'], config['user']]):
            self.log(f"[错误] {db_type} 数据库的主机和用户名不能为空。")
            return

        self.log(f"正在测试 {db_type} 数据库连接...")

        # 在新线程中测试连接，避免UI冻结
        thread = threading.Thread(
            target=lambda: migration_core.test_db_connection(config, self.log),
            daemon=True
        )
        thread.start()

    def save_config(self):
        config = self.get_configs_from_ui()
        try:
            with open(CONFIG_FILE, 'w') as f:
                json.dump(config, f, indent=4)
            self.log("配置已成功保存到 " + CONFIG_FILE)
        except Exception as e:
            self.log(f"错误: 无法保存配置: {e}")

    def load_config(self):
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r') as f:
                    config = json.load(f)
                
                self.entries['source_主机/ip'].insert(0, config['source'].get('host', ''))
                self.entries['source_端口'].insert(0, config['source'].get('port', '3306'))
                self.entries['source_用户名'].insert(0, config['source'].get('user', ''))
                self.entries['source_密码'].insert(0, config['source'].get('password', ''))
                
                self.entries['dest_主机/ip'].insert(0, config['dest'].get('host', ''))
                self.entries['dest_端口'].insert(0, config['dest'].get('port', '3306'))
                self.entries['dest_用户名'].insert(0, config['dest'].get('user', ''))
                self.entries['dest_密码'].insert(0, config['dest'].get('password', ''))

                self.log("已从 " + CONFIG_FILE + " 加载配置。")
        except Exception as e:
            self.log(f"提示: 未找到或无法加载配置文件，请手动输入。({e})")

    def get_configs_from_ui(self):
        """从UI获取数据库配置，确保字符集兼容性"""
        return {
            "source": {
                'host': self.entries['source_主机/ip'].get().strip(),
                'port': int(self.entries['source_端口'].get().strip() or 3306),
                'user': self.entries['source_用户名'].get().strip(),
                'password': self.entries['source_密码'].get(),
                'charset': 'utf8mb3',
                'use_unicode': True
            },
            "dest": {
                'host': self.entries['dest_主机/ip'].get().strip(),
                'port': int(self.entries['dest_端口'].get().strip() or 3306),
                'user': self.entries['dest_用户名'].get().strip(),
                'password': self.entries['dest_密码'].get(),
                'charset': 'utf8mb4',
                'use_unicode': True
            }
        }
    
    def set_ui_state(self, is_running):
        self.is_running = is_running
        state = tk.DISABLED if is_running else tk.NORMAL
        for widget in self.winfo_children():
            if isinstance(widget, ttk.Frame) or isinstance(widget, ttk.Labelframe):
                for child in widget.winfo_children():
                     if isinstance(child, (ttk.Button, ttk.Entry, ttk.Radiobutton, ttk.Checkbutton)):
                        child.config(state=state)
        # 总是让日志清空按钮可用
        self.start_button.config(state=state)
        self.log_area.config(state='disabled') # 日志区总是禁用编辑
        
    def start_migration(self):
        if self.is_running:
            return

        configs = self.get_configs_from_ui()
        task = self.migration_task.get()
        direct_execute = self.direct_execute_users.get()

        # 验证配置
        if not self.validate_configs(configs):
            return

        # 启动一个新线程来运行迁移，避免UI冻结
        self.set_ui_state(True)
        thread = threading.Thread(target=self.run_migration_thread, args=(configs, task, direct_execute), daemon=True)
        thread.start()

    def validate_configs(self, configs):
        """验证数据库配置"""
        for db_type, config in configs.items():
            db_name = "源数据库" if db_type == "source" else "目标数据库"

            if not config['host'].strip():
                self.log(f"[错误] {db_name}的主机/IP不能为空")
                return False
            if not config['user'].strip():
                self.log(f"[错误] {db_name}的用户名不能为空")
                return False
            if config['port'] < 1 or config['port'] > 65535:
                self.log(f"[错误] {db_name}的端口号必须在1-65535之间")
                return False

        return True

    def run_migration_thread(self, configs, task, direct_execute):
        self.log("\n==============================================")
        self.log("          开始执行迁移任务...           ")
        self.log("==============================================")

        # 添加调试信息
        self.log(f"调试: 迁移任务类型 - {task}")
        self.log(f"调试: 源数据库配置 - host={configs['source']['host']}, port={configs['source']['port']}, user={configs['source']['user']}, charset={configs['source'].get('charset', 'N/A')}")
        self.log(f"调试: 目标数据库配置 - host={configs['dest']['host']}, port={configs['dest']['port']}, user={configs['dest']['user']}, charset={configs['dest'].get('charset', 'N/A')}")

        success = True
        try:
            if task == "data":
                success = migration_core.migrate_data_and_structure(configs['source'], configs['dest'], self.log)
            elif task == "users":
                success = migration_core.migrate_users_and_permissions(configs['source'], configs['dest'], self.log, direct_execute)
            elif task == "full":
                self.log("步骤 1/2: 迁移数据和结构")
                data_success = migration_core.migrate_data_and_structure(configs['source'], configs['dest'], self.log)
                if data_success:
                    # 添加小延迟，确保前一个连接完全关闭
                    import time
                    import gc
                    self.log("等待连接资源释放 (2秒)...")
                    # 强制垃圾回收，确保连接资源完全释放
                    gc.collect()
                    time.sleep(2)
                    
                    self.log("\n步骤 2/2: 迁移用户和权限")
                    user_success = migration_core.migrate_users_and_permissions(configs['source'], configs['dest'], self.log, direct_execute)
                    success = user_success
                else:
                    success = False
        except Exception as e:
            self.log(f"\n[致命错误] 迁移线程异常: {e}")
            success = False
        
        if success:
            self.log("\n🎉🎉🎉 所有任务成功完成！ 🎉🎉🎉")
        else:
            self.log("\n❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌")
            
        self.set_ui_state(False)

    def on_closing(self):
        if self.is_running:
            if messagebox.askokcancel("退出", "迁移任务仍在进行中，确定要强制退出吗？"):
                self.destroy()
        else:
            self.destroy()

if __name__ == "__main__":
    app = MigrationApp()
    app.mainloop() 