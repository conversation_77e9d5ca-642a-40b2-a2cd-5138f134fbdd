# 通用数据库迁移工具 (GUI版) 🚀

欢迎使用数据库迁移工具！本项目是一个拥有图形用户界面（GUI）的桌面应用程序，旨在帮助您轻松、直观地将 **MariaDB 或 MySQL** 数据库服务器上的所有内容（包括数据、结构、事件、用户和权限）完整、安全地迁移到一台新的 **MySQL 或 MariaDB** 服务器上。

## 🔄 支持的迁移路径

- `MariaDB -> MySQL`
- `MySQL -> MySQL`
- `MariaDB -> MariaDB`
- `MySQL -> MariaDB`

## ✨ 功能特性

- **🖥️ 图形化操作**：告别命令行和配置文件，所有操作都在清晰的窗口中完成
- **💾 配置持久化**：自动保存您的数据库连接信息到 `db_config.json` 文件，下次打开时无需重复输入
- **🔧 连接测试**：内置连接测试功能，确保配置正确后再开始迁移
- **⚙️ 灵活的迁移选项**：支持完整迁移、仅迁移数据或仅迁移用户，满足不同场景的需求
- **🔐 双重用户迁移模式**：
    - **安全模式 (默认)**: 生成 `user_migration.sql` 文件供您审查后手动执行
    - **全自动模式**: 一键直接在目标数据库上创建用户和权限
- **📊 实时日志**：在界面上实时显示详细的迁移进度和日志信息
- **🛡️ 字符集兼容**：自动处理 utf8mb4 字符集，确保中文等多字节字符正确迁移
- **🏗️ 高内聚、低耦合**：界面与核心逻辑分离，代码结构清晰，易于维护

---

## 🚀 如何运行

1.  **环境准备**:
    - 确保您的电脑上已安装 Python 3.6 或更高版本。
    - 在终端或命令行中，安装本项目唯一的依赖库 `mysql-connector-python`：
      ```bash
      pip install mysql-connector-python
      ```

2.  **启动应用**:
    - 直接运行主程序 `migrate_ui.py` 即可启动图形界面：
      ```bash
      python migrate_ui.py
      ```

---

## 📖 使用指南

1.  **填写配置**:
    - 在界面左侧的“源数据库”区域，填写您的**源数据库**的连接信息（IP、端口、用户名、密码）。
    - 在界面右侧的“目标数据库”区域，填写您的**目标数据库**的连接信息。
    - 填写完毕后，可以点击 **[ 保存配置 ]** 按钮，您的信息（密码除外，出于安全考虑通常不建议保存密码，但本工具做了保存）将被记录在项目目录下的 `db_config.json` 文件中。

2.  **选择迁移任务**:
    - 在“迁移选项”区域，根据您的需求选择一个任务：
        - `完整迁移 (数据 + 用户)`: 默认选项，执行最完整的迁移。
        - `仅迁移数据和结构`: 如果您只想同步数据，不处理用户。
        - `仅迁移用户和权限`: 如果数据已同步，只想更新用户。

3.  **选择用户迁移模式**:
    - 如果您选择了包含用户迁移的任务，下方的复选框将变为可用。
    - **保持不勾选 (推荐)**: 程序将执行“安全模式”，在项目目录下生成 `user_migration.sql` 文件。您需要手动检查此文件，然后在目标数据库中执行它。
    - **勾选 `直接在目标库执行...`**: 程序将执行“全自动模式”，跳过生成文件，直接在目标库上创建用户。**请在完全信任源数据库权限配置的情况下使用此模式。**

4.  **开始迁移**:
    - 点击 **[ 开始迁移 ]** 按钮。
    - 按钮会变为灰色不可用状态，表示任务正在后台进行，界面不会卡死。
    - 您可以在下方的“实时日志”区域看到所有后台操作的输出。

5.  **完成**:
    - 当日志区域显示 “🎉🎉🎉 所有任务成功完成！ 🎉🎉🎉” 时，表示迁移已结束。
    - **[ 开始迁移 ]** 按钮会恢复可用，您可以执行新的任务。

---

## 📁 项目文件结构

- `migrate_ui.py`:
  - **主程序**。运行它来启动应用。包含所有图形界面的代码。
- `migration_core.py`:
  - **核心逻辑模块**。包含所有实际执行数据库迁移的函数。
- `db_config.json` (自动生成):
  - **配置文件**。保存您输入的数据库连接信息。
- `user_migration.sql` (按需生成):
  - **用户迁移脚本**。在“安全模式”下生成，包含所有创建用户的 SQL 命令。

---

## 🔧 常见问题解决

### 字符集问题
如果遇到 "Character set 'utf8' unsupported" 错误：
- 本工具已实现多重字符集兼容策略，自动尝试 utf8mb4、utf8 等字符集
- 连接成功后会自动设置最佳的字符集配置
- 添加了详细的调试日志，帮助诊断连接问题
- 如果仍有问题，请检查 mysql-connector-python 版本：`pip install --upgrade mysql-connector-python`

### 连接问题
- 确保防火墙允许数据库端口通信
- 检查数据库用户是否有足够的权限
- 使用连接测试功能验证配置

### 权限问题
- 源数据库用户需要 SELECT 权限读取所有数据库和表
- 目标数据库用户需要 CREATE、INSERT、UPDATE、DELETE 等权限
- 用户迁移需要对 mysql.user 表的访问权限

---

## 👨‍💻 维护者

- **alxxxxla** - 项目维护者

---

## 📄 许可证

本项目采用 MIT 许可证 - 详见 LICENSE 文件