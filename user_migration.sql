-- # 用户和权限迁移脚本 (请在目标服务器上执行)

CREATE USER IF NOT EXISTS 'gi'@'%' IDENTIFIED WITH mysql_native_password BY 'please_change_password_after_migration';
GRANT ALL PRIVILEGES ON `gi888`.* TO `gi`@`%`;
CREATE USER IF NOT EXISTS 'taobao'@'%' IDENTIFIED WITH mysql_native_password BY 'please_change_password_after_migration';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, REFERENCES, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, SHOW VIEW, CREATE ROUTINE, ALTER ROUTINE, EVENT, TRIGGER ON `taobao_db`.* TO `taobao`@`%`;

FLUSH PRIVILEGES;